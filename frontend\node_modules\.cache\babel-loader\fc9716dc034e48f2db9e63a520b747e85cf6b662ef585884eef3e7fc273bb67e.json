{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"field\", \"id\", \"value\", \"formattedValue\", \"row\", \"rowNode\", \"colDef\", \"isEditable\", \"cellMode\", \"hasFocus\", \"tabIndex\", \"api\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { unstable_composeClasses as composeClasses, unstable_useForkRef as useForkRef } from '@mui/utils';\nimport { isSpaceKey } from '../../utils/keyboardUtils';\nimport { useGridApiContext } from '../../hooks/utils/useGridApiContext';\nimport { useGridRootProps } from '../../hooks/utils/useGridRootProps';\nimport { getDataGridUtilityClass } from '../../constants/gridClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['checkboxInput']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, classes);\n};\nconst GridCellCheckboxForwardRef = /*#__PURE__*/React.forwardRef(function GridCellCheckboxRenderer(props, ref) {\n  var _rootProps$slotProps;\n  const {\n      field,\n      id,\n      value: isChecked,\n      rowNode,\n      hasFocus,\n      tabIndex\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const apiRef = useGridApiContext();\n  const rootProps = useGridRootProps();\n  const ownerState = {\n    classes: rootProps.classes\n  };\n  const classes = useUtilityClasses(ownerState);\n  const checkboxElement = React.useRef(null);\n  const rippleRef = React.useRef(null);\n  const handleRef = useForkRef(checkboxElement, ref);\n  const element = apiRef.current.getCellElement(id, field);\n  const handleChange = event => {\n    const params = {\n      value: event.target.checked,\n      id\n    };\n    apiRef.current.publishEvent('rowSelectionCheckboxChange', params, event);\n  };\n  React.useLayoutEffect(() => {\n    if (tabIndex === 0 && element) {\n      element.tabIndex = -1;\n    }\n  }, [element, tabIndex]);\n  React.useEffect(() => {\n    if (hasFocus) {\n      var _checkboxElement$curr;\n      const input = (_checkboxElement$curr = checkboxElement.current) == null ? void 0 : _checkboxElement$curr.querySelector('input');\n      input == null || input.focus({\n        preventScroll: true\n      });\n    } else if (rippleRef.current) {\n      // Only available in @mui/material v5.4.1 or later\n      rippleRef.current.stop({});\n    }\n  }, [hasFocus]);\n  const handleKeyDown = React.useCallback(event => {\n    if (isSpaceKey(event.key)) {\n      // We call event.stopPropagation to avoid selecting the row and also scrolling to bottom\n      // TODO: Remove and add a check inside useGridKeyboardNavigation\n      event.stopPropagation();\n    }\n  }, []);\n  if (rowNode.type === 'footer' || rowNode.type === 'pinnedRow') {\n    return null;\n  }\n  const isSelectable = apiRef.current.isRowSelectable(id);\n  const label = apiRef.current.getLocaleText(isChecked ? 'checkboxSelectionUnselectRow' : 'checkboxSelectionSelectRow');\n  return /*#__PURE__*/_jsx(rootProps.slots.baseCheckbox, _extends({\n    ref: handleRef,\n    tabIndex: tabIndex,\n    checked: isChecked,\n    onChange: handleChange,\n    className: classes.root,\n    inputProps: {\n      'aria-label': label\n    },\n    onKeyDown: handleKeyDown,\n    disabled: !isSelectable,\n    touchRippleRef: rippleRef\n  }, (_rootProps$slotProps = rootProps.slotProps) == null ? void 0 : _rootProps$slotProps.baseCheckbox, other));\n});\nprocess.env.NODE_ENV !== \"production\" ? GridCellCheckboxForwardRef.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * GridApi that let you manipulate the grid.\n   */\n  api: PropTypes.object.isRequired,\n  /**\n   * The mode of the cell.\n   */\n  cellMode: PropTypes.oneOf(['edit', 'view']).isRequired,\n  /**\n   * The column of the row that the current cell belongs to.\n   */\n  colDef: PropTypes.object.isRequired,\n  /**\n   * The column field of the cell that triggered the event.\n   */\n  field: PropTypes.string.isRequired,\n  /**\n   * A ref allowing to set imperative focus.\n   * It can be passed to the element that should receive focus.\n   * @ignore - do not document.\n   */\n  focusElementRef: PropTypes.oneOfType([PropTypes.func, PropTypes.shape({\n    current: PropTypes.shape({\n      focus: PropTypes.func.isRequired\n    })\n  })]),\n  /**\n   * The cell value formatted with the column valueFormatter.\n   */\n  formattedValue: PropTypes.any,\n  /**\n   * If true, the cell is the active element.\n   */\n  hasFocus: PropTypes.bool.isRequired,\n  /**\n   * The grid row id.\n   */\n  id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).isRequired,\n  /**\n   * If true, the cell is editable.\n   */\n  isEditable: PropTypes.bool,\n  /**\n   * The row model of the row that the current cell belongs to.\n   */\n  row: PropTypes.any.isRequired,\n  /**\n   * The node of the row that the current cell belongs to.\n   */\n  rowNode: PropTypes.object.isRequired,\n  /**\n   * the tabIndex value.\n   */\n  tabIndex: PropTypes.oneOf([-1, 0]).isRequired,\n  /**\n   * The cell value.\n   * If the column has `valueGetter`, use `params.row` to directly access the fields.\n   */\n  value: PropTypes.any\n} : void 0;\nexport { GridCellCheckboxForwardRef };\nexport const GridCellCheckboxRenderer = GridCellCheckboxForwardRef;", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "PropTypes", "unstable_composeClasses", "composeClasses", "unstable_useForkRef", "useForkRef", "isSpaceKey", "useGridApiContext", "useGridRootProps", "getDataGridUtilityClass", "jsx", "_jsx", "useUtilityClasses", "ownerState", "classes", "slots", "root", "GridCellCheckboxForwardRef", "forwardRef", "GridCellCheckboxRenderer", "props", "ref", "_rootProps$slotProps", "field", "id", "value", "isChecked", "rowNode", "hasFocus", "tabIndex", "other", "apiRef", "rootProps", "checkboxElement", "useRef", "rippleRef", "handleRef", "element", "current", "getCellElement", "handleChange", "event", "params", "target", "checked", "publishEvent", "useLayoutEffect", "useEffect", "_checkboxElement$curr", "input", "querySelector", "focus", "preventScroll", "stop", "handleKeyDown", "useCallback", "key", "stopPropagation", "type", "isSelectable", "isRowSelectable", "label", "getLocaleText", "baseCheckbox", "onChange", "className", "inputProps", "onKeyDown", "disabled", "touchRippleRef", "slotProps", "process", "env", "NODE_ENV", "propTypes", "api", "object", "isRequired", "cellMode", "oneOf", "colDef", "string", "focusElementRef", "oneOfType", "func", "shape", "formattedValue", "any", "bool", "number", "isEditable", "row"], "sources": ["C:/Users/<USER>/Desktop/Test_HTML/Material Managment1/frontend/node_modules/@mui/x-data-grid/components/columnSelection/GridCellCheckboxRenderer.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"field\", \"id\", \"value\", \"formattedValue\", \"row\", \"rowNode\", \"colDef\", \"isEditable\", \"cellMode\", \"hasFocus\", \"tabIndex\", \"api\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { unstable_composeClasses as composeClasses, unstable_useForkRef as useForkRef } from '@mui/utils';\nimport { isSpaceKey } from '../../utils/keyboardUtils';\nimport { useGridApiContext } from '../../hooks/utils/useGridApiContext';\nimport { useGridRootProps } from '../../hooks/utils/useGridRootProps';\nimport { getDataGridUtilityClass } from '../../constants/gridClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['checkboxInput']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, classes);\n};\nconst GridCellCheckboxForwardRef = /*#__PURE__*/React.forwardRef(function GridCellCheckboxRenderer(props, ref) {\n  var _rootProps$slotProps;\n  const {\n      field,\n      id,\n      value: isChecked,\n      rowNode,\n      hasFocus,\n      tabIndex\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const apiRef = useGridApiContext();\n  const rootProps = useGridRootProps();\n  const ownerState = {\n    classes: rootProps.classes\n  };\n  const classes = useUtilityClasses(ownerState);\n  const checkboxElement = React.useRef(null);\n  const rippleRef = React.useRef(null);\n  const handleRef = useForkRef(checkboxElement, ref);\n  const element = apiRef.current.getCellElement(id, field);\n  const handleChange = event => {\n    const params = {\n      value: event.target.checked,\n      id\n    };\n    apiRef.current.publishEvent('rowSelectionCheckboxChange', params, event);\n  };\n  React.useLayoutEffect(() => {\n    if (tabIndex === 0 && element) {\n      element.tabIndex = -1;\n    }\n  }, [element, tabIndex]);\n  React.useEffect(() => {\n    if (hasFocus) {\n      var _checkboxElement$curr;\n      const input = (_checkboxElement$curr = checkboxElement.current) == null ? void 0 : _checkboxElement$curr.querySelector('input');\n      input == null || input.focus({\n        preventScroll: true\n      });\n    } else if (rippleRef.current) {\n      // Only available in @mui/material v5.4.1 or later\n      rippleRef.current.stop({});\n    }\n  }, [hasFocus]);\n  const handleKeyDown = React.useCallback(event => {\n    if (isSpaceKey(event.key)) {\n      // We call event.stopPropagation to avoid selecting the row and also scrolling to bottom\n      // TODO: Remove and add a check inside useGridKeyboardNavigation\n      event.stopPropagation();\n    }\n  }, []);\n  if (rowNode.type === 'footer' || rowNode.type === 'pinnedRow') {\n    return null;\n  }\n  const isSelectable = apiRef.current.isRowSelectable(id);\n  const label = apiRef.current.getLocaleText(isChecked ? 'checkboxSelectionUnselectRow' : 'checkboxSelectionSelectRow');\n  return /*#__PURE__*/_jsx(rootProps.slots.baseCheckbox, _extends({\n    ref: handleRef,\n    tabIndex: tabIndex,\n    checked: isChecked,\n    onChange: handleChange,\n    className: classes.root,\n    inputProps: {\n      'aria-label': label\n    },\n    onKeyDown: handleKeyDown,\n    disabled: !isSelectable,\n    touchRippleRef: rippleRef\n  }, (_rootProps$slotProps = rootProps.slotProps) == null ? void 0 : _rootProps$slotProps.baseCheckbox, other));\n});\nprocess.env.NODE_ENV !== \"production\" ? GridCellCheckboxForwardRef.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * GridApi that let you manipulate the grid.\n   */\n  api: PropTypes.object.isRequired,\n  /**\n   * The mode of the cell.\n   */\n  cellMode: PropTypes.oneOf(['edit', 'view']).isRequired,\n  /**\n   * The column of the row that the current cell belongs to.\n   */\n  colDef: PropTypes.object.isRequired,\n  /**\n   * The column field of the cell that triggered the event.\n   */\n  field: PropTypes.string.isRequired,\n  /**\n   * A ref allowing to set imperative focus.\n   * It can be passed to the element that should receive focus.\n   * @ignore - do not document.\n   */\n  focusElementRef: PropTypes.oneOfType([PropTypes.func, PropTypes.shape({\n    current: PropTypes.shape({\n      focus: PropTypes.func.isRequired\n    })\n  })]),\n  /**\n   * The cell value formatted with the column valueFormatter.\n   */\n  formattedValue: PropTypes.any,\n  /**\n   * If true, the cell is the active element.\n   */\n  hasFocus: PropTypes.bool.isRequired,\n  /**\n   * The grid row id.\n   */\n  id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).isRequired,\n  /**\n   * If true, the cell is editable.\n   */\n  isEditable: PropTypes.bool,\n  /**\n   * The row model of the row that the current cell belongs to.\n   */\n  row: PropTypes.any.isRequired,\n  /**\n   * The node of the row that the current cell belongs to.\n   */\n  rowNode: PropTypes.object.isRequired,\n  /**\n   * the tabIndex value.\n   */\n  tabIndex: PropTypes.oneOf([-1, 0]).isRequired,\n  /**\n   * The cell value.\n   * If the column has `valueGetter`, use `params.row` to directly access the fields.\n   */\n  value: PropTypes.any\n} : void 0;\nexport { GridCellCheckboxForwardRef };\nexport const GridCellCheckboxRenderer = GridCellCheckboxForwardRef;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,gBAAgB,EAAE,KAAK,EAAE,SAAS,EAAE,QAAQ,EAAE,YAAY,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,KAAK,CAAC;AACjJ,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,uBAAuB,IAAIC,cAAc,EAAEC,mBAAmB,IAAIC,UAAU,QAAQ,YAAY;AACzG,SAASC,UAAU,QAAQ,2BAA2B;AACtD,SAASC,iBAAiB,QAAQ,qCAAqC;AACvE,SAASC,gBAAgB,QAAQ,oCAAoC;AACrE,SAASC,uBAAuB,QAAQ,6BAA6B;AACrE,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC;EACF,CAAC,GAAGD,UAAU;EACd,MAAME,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,eAAe;EACxB,CAAC;EACD,OAAOb,cAAc,CAACY,KAAK,EAAEN,uBAAuB,EAAEK,OAAO,CAAC;AAChE,CAAC;AACD,MAAMG,0BAA0B,GAAG,aAAajB,KAAK,CAACkB,UAAU,CAAC,SAASC,wBAAwBA,CAACC,KAAK,EAAEC,GAAG,EAAE;EAC7G,IAAIC,oBAAoB;EACxB,MAAM;MACFC,KAAK;MACLC,EAAE;MACFC,KAAK,EAAEC,SAAS;MAChBC,OAAO;MACPC,QAAQ;MACRC;IACF,CAAC,GAAGT,KAAK;IACTU,KAAK,GAAGhC,6BAA6B,CAACsB,KAAK,EAAErB,SAAS,CAAC;EACzD,MAAMgC,MAAM,GAAGxB,iBAAiB,CAAC,CAAC;EAClC,MAAMyB,SAAS,GAAGxB,gBAAgB,CAAC,CAAC;EACpC,MAAMK,UAAU,GAAG;IACjBC,OAAO,EAAEkB,SAAS,CAAClB;EACrB,CAAC;EACD,MAAMA,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAMoB,eAAe,GAAGjC,KAAK,CAACkC,MAAM,CAAC,IAAI,CAAC;EAC1C,MAAMC,SAAS,GAAGnC,KAAK,CAACkC,MAAM,CAAC,IAAI,CAAC;EACpC,MAAME,SAAS,GAAG/B,UAAU,CAAC4B,eAAe,EAAEZ,GAAG,CAAC;EAClD,MAAMgB,OAAO,GAAGN,MAAM,CAACO,OAAO,CAACC,cAAc,CAACf,EAAE,EAAED,KAAK,CAAC;EACxD,MAAMiB,YAAY,GAAGC,KAAK,IAAI;IAC5B,MAAMC,MAAM,GAAG;MACbjB,KAAK,EAAEgB,KAAK,CAACE,MAAM,CAACC,OAAO;MAC3BpB;IACF,CAAC;IACDO,MAAM,CAACO,OAAO,CAACO,YAAY,CAAC,4BAA4B,EAAEH,MAAM,EAAED,KAAK,CAAC;EAC1E,CAAC;EACDzC,KAAK,CAAC8C,eAAe,CAAC,MAAM;IAC1B,IAAIjB,QAAQ,KAAK,CAAC,IAAIQ,OAAO,EAAE;MAC7BA,OAAO,CAACR,QAAQ,GAAG,CAAC,CAAC;IACvB;EACF,CAAC,EAAE,CAACQ,OAAO,EAAER,QAAQ,CAAC,CAAC;EACvB7B,KAAK,CAAC+C,SAAS,CAAC,MAAM;IACpB,IAAInB,QAAQ,EAAE;MACZ,IAAIoB,qBAAqB;MACzB,MAAMC,KAAK,GAAG,CAACD,qBAAqB,GAAGf,eAAe,CAACK,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGU,qBAAqB,CAACE,aAAa,CAAC,OAAO,CAAC;MAC/HD,KAAK,IAAI,IAAI,IAAIA,KAAK,CAACE,KAAK,CAAC;QAC3BC,aAAa,EAAE;MACjB,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIjB,SAAS,CAACG,OAAO,EAAE;MAC5B;MACAH,SAAS,CAACG,OAAO,CAACe,IAAI,CAAC,CAAC,CAAC,CAAC;IAC5B;EACF,CAAC,EAAE,CAACzB,QAAQ,CAAC,CAAC;EACd,MAAM0B,aAAa,GAAGtD,KAAK,CAACuD,WAAW,CAACd,KAAK,IAAI;IAC/C,IAAInC,UAAU,CAACmC,KAAK,CAACe,GAAG,CAAC,EAAE;MACzB;MACA;MACAf,KAAK,CAACgB,eAAe,CAAC,CAAC;IACzB;EACF,CAAC,EAAE,EAAE,CAAC;EACN,IAAI9B,OAAO,CAAC+B,IAAI,KAAK,QAAQ,IAAI/B,OAAO,CAAC+B,IAAI,KAAK,WAAW,EAAE;IAC7D,OAAO,IAAI;EACb;EACA,MAAMC,YAAY,GAAG5B,MAAM,CAACO,OAAO,CAACsB,eAAe,CAACpC,EAAE,CAAC;EACvD,MAAMqC,KAAK,GAAG9B,MAAM,CAACO,OAAO,CAACwB,aAAa,CAACpC,SAAS,GAAG,8BAA8B,GAAG,4BAA4B,CAAC;EACrH,OAAO,aAAaf,IAAI,CAACqB,SAAS,CAACjB,KAAK,CAACgD,YAAY,EAAElE,QAAQ,CAAC;IAC9DwB,GAAG,EAAEe,SAAS;IACdP,QAAQ,EAAEA,QAAQ;IAClBe,OAAO,EAAElB,SAAS;IAClBsC,QAAQ,EAAExB,YAAY;IACtByB,SAAS,EAAEnD,OAAO,CAACE,IAAI;IACvBkD,UAAU,EAAE;MACV,YAAY,EAAEL;IAChB,CAAC;IACDM,SAAS,EAAEb,aAAa;IACxBc,QAAQ,EAAE,CAACT,YAAY;IACvBU,cAAc,EAAElC;EAClB,CAAC,EAAE,CAACb,oBAAoB,GAAGU,SAAS,CAACsC,SAAS,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGhD,oBAAoB,CAACyC,YAAY,EAAEjC,KAAK,CAAC,CAAC;AAC/G,CAAC,CAAC;AACFyC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGxD,0BAA0B,CAACyD,SAAS,GAAG;EAC7E;EACA;EACA;EACA;EACA;AACF;AACA;EACEC,GAAG,EAAE1E,SAAS,CAAC2E,MAAM,CAACC,UAAU;EAChC;AACF;AACA;EACEC,QAAQ,EAAE7E,SAAS,CAAC8E,KAAK,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAACF,UAAU;EACtD;AACF;AACA;EACEG,MAAM,EAAE/E,SAAS,CAAC2E,MAAM,CAACC,UAAU;EACnC;AACF;AACA;EACEtD,KAAK,EAAEtB,SAAS,CAACgF,MAAM,CAACJ,UAAU;EAClC;AACF;AACA;AACA;AACA;EACEK,eAAe,EAAEjF,SAAS,CAACkF,SAAS,CAAC,CAAClF,SAAS,CAACmF,IAAI,EAAEnF,SAAS,CAACoF,KAAK,CAAC;IACpE/C,OAAO,EAAErC,SAAS,CAACoF,KAAK,CAAC;MACvBlC,KAAK,EAAElD,SAAS,CAACmF,IAAI,CAACP;IACxB,CAAC;EACH,CAAC,CAAC,CAAC,CAAC;EACJ;AACF;AACA;EACES,cAAc,EAAErF,SAAS,CAACsF,GAAG;EAC7B;AACF;AACA;EACE3D,QAAQ,EAAE3B,SAAS,CAACuF,IAAI,CAACX,UAAU;EACnC;AACF;AACA;EACErD,EAAE,EAAEvB,SAAS,CAACkF,SAAS,CAAC,CAAClF,SAAS,CAACwF,MAAM,EAAExF,SAAS,CAACgF,MAAM,CAAC,CAAC,CAACJ,UAAU;EACxE;AACF;AACA;EACEa,UAAU,EAAEzF,SAAS,CAACuF,IAAI;EAC1B;AACF;AACA;EACEG,GAAG,EAAE1F,SAAS,CAACsF,GAAG,CAACV,UAAU;EAC7B;AACF;AACA;EACElD,OAAO,EAAE1B,SAAS,CAAC2E,MAAM,CAACC,UAAU;EACpC;AACF;AACA;EACEhD,QAAQ,EAAE5B,SAAS,CAAC8E,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACF,UAAU;EAC7C;AACF;AACA;AACA;EACEpD,KAAK,EAAExB,SAAS,CAACsF;AACnB,CAAC,GAAG,KAAK,CAAC;AACV,SAAStE,0BAA0B;AACnC,OAAO,MAAME,wBAAwB,GAAGF,0BAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}