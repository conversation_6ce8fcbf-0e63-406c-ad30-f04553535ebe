{"ast": null, "code": "import generateUtilityClass from '@mui/material/generateUtilityClass';\nimport generateUtilityClasses from '@mui/material/generateUtilityClasses';\nexport function getTimelineConnectorUtilityClass(slot) {\n  return generateUtilityClass('MuiTimelineConnector', slot);\n}\nconst timelineConnectorClasses = generateUtilityClasses('MuiTimelineConnector', ['root']);\nexport default timelineConnectorClasses;", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getTimelineConnectorUtilityClass", "slot", "timelineConnectorClasses"], "sources": ["C:/Users/<USER>/Desktop/Test_HTML/Material Managment1/frontend/node_modules/@mui/lab/TimelineConnector/timelineConnectorClasses.js"], "sourcesContent": ["import generateUtilityClass from '@mui/material/generateUtilityClass';\nimport generateUtilityClasses from '@mui/material/generateUtilityClasses';\nexport function getTimelineConnectorUtilityClass(slot) {\n  return generateUtilityClass('MuiTimelineConnector', slot);\n}\nconst timelineConnectorClasses = generateUtilityClasses('MuiTimelineConnector', ['root']);\nexport default timelineConnectorClasses;"], "mappings": "AAAA,OAAOA,oBAAoB,MAAM,oCAAoC;AACrE,OAAOC,sBAAsB,MAAM,sCAAsC;AACzE,OAAO,SAASC,gCAAgCA,CAACC,IAAI,EAAE;EACrD,OAAOH,oBAAoB,CAAC,sBAAsB,EAAEG,IAAI,CAAC;AAC3D;AACA,MAAMC,wBAAwB,GAAGH,sBAAsB,CAAC,sBAAsB,EAAE,CAAC,MAAM,CAAC,CAAC;AACzF,eAAeG,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}