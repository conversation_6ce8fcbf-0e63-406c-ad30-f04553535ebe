"""
Simplified METS permissions models that work with default Django User model
"""

from django.contrib.auth.models import User
from django.db import models
from django.utils import timezone


class METSPermission(models.Model):
    """
    METS-specific permissions for material request workflow
    """
    PERMISSION_CHOICES = [
        # Material Request Creation and Validation
        ('create_material_request', 'Create New Material Request'),
        ('validate_material_request', 'Validate Material Request Details'),
        ('initial_approve_mr', 'Give Initial Approval for Material Request'),
        
        # Technical Evaluation Permissions
        ('start_technical_eval', 'Start Technical Evaluation Process'),
        ('perform_technical_eval', 'Perform Technical Evaluation'),
        ('approve_technical_eval', 'Approve Technical Evaluation'),
        ('reject_technical_eval', 'Reject Technical Evaluation'),
        ('request_tech_clarification', 'Request Technical Clarification'),
        
        # Commercial Evaluation Permissions
        ('start_commercial_eval', 'Start Commercial Evaluation Process'),
        ('perform_commercial_eval', 'Perform Commercial Evaluation'),
        ('approve_commercial_eval', 'Approve Commercial Evaluation'),
        ('reject_commercial_eval', 'Reject Commercial Evaluation'),
        ('request_comm_clarification', 'Request Commercial Clarification'),
        
        # Purchase Process Permissions
        ('initiate_purchase', 'Initiate Purchase Process'),
        ('create_purchase_order', 'Create Purchase Order'),
        ('approve_purchase_order', 'Approve Purchase Order'),
        ('send_to_supplier', 'Send Purchase Order to Supplier'),
        
        # Warehouse and Receiving Permissions
        ('record_partial_delivery', 'Record Partial Delivery'),
        ('perform_receiving_inspection', 'Perform Receiving Inspection'),
        ('warehouse_receive', 'Receive Items in Warehouse'),
        ('confirm_final_delivery', 'Confirm Final Delivery to Requester'),
        
        # Administrative Permissions
        ('view_only', 'View-Only Access'),
        ('manage_workflow', 'Manage Workflow Configuration'),
        ('override_workflow', 'Override Workflow State'),
        ('view_reports', 'View Reports and Analytics'),
        
        # Special Permissions
        ('expedite_request', 'Expedite Material Request'),
        ('cancel_request', 'Cancel Material Request'),
        ('reactivate_request', 'Reactivate Cancelled Request'),
    ]
    
    code = models.CharField(max_length=100, choices=PERMISSION_CHOICES, unique=True)
    name = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    category = models.CharField(max_length=50, default='material_request')
    is_active = models.BooleanField(default=True)
    
    # Workflow state restrictions
    applicable_states = models.JSONField(
        default=list, 
        help_text="List of workflow states where this permission applies"
    )
    
    # Role restrictions
    restricted_to_roles = models.JSONField(
        default=list,
        help_text="List of roles that can have this permission"
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'mets_permissions'
        verbose_name = 'METS Permission'
        verbose_name_plural = 'METS Permissions'
        ordering = ['category', 'name']
    
    def __str__(self):
        return self.name
    
    @classmethod
    def get_permissions_for_role(cls, role):
        """Get all permissions available for a specific role"""
        return cls.objects.filter(
            models.Q(restricted_to_roles__contains=[role]) | 
            models.Q(restricted_to_roles=[]),
            is_active=True
        )
    
    @classmethod
    def get_permissions_for_state(cls, state):
        """Get all permissions applicable for a specific workflow state"""
        return cls.objects.filter(
            models.Q(applicable_states__contains=[state]) |
            models.Q(applicable_states=[]),
            is_active=True
        )


class UserMETSProfile(models.Model):
    """
    METS profile extension for Django User model
    """
    ROLE_CHOICES = [
        ('ms', 'Material Specialist'),
        ('tb', 'Technical Buyer'),
        ('mc', 'Material Coordinator'),
        ('sv', 'Supervisor'),
        ('esh', 'Expat Section Head'),
        ('lsh', 'Local Section Head'),
        ('cm', 'Committee Member'),
        ('admin', 'System Administrator'),
        ('tech', 'Technical Evaluator'),
        ('comm', 'Commercial Evaluator'),
        ('wh', 'Warehouse Coordinator'),
        ('rcv', 'Receiving Inspector'),
    ]
    
    DEPARTMENT_CHOICES = [
        ('mechanical', 'Mechanical'),
        ('electrical', 'Electrical'),
        ('instrumentation', 'Instrumentation'),
        ('civil', 'Civil'),
        ('process', 'Process'),
        ('safety', 'Safety'),
        ('maintenance', 'Maintenance'),
        ('procurement', 'Procurement'),
    ]
    
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='mets_profile')
    
    # METS specific fields
    role = models.CharField(max_length=10, choices=ROLE_CHOICES, default='ms')
    department = models.CharField(max_length=20, choices=DEPARTMENT_CHOICES, blank=True)
    employee_id = models.CharField(max_length=20, blank=True)
    auth_level = models.PositiveIntegerField(default=1, help_text="Higher numbers = more authority")
    
    # Contact information
    phone = models.CharField(max_length=20, blank=True)
    office_location = models.CharField(max_length=100, blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'user_mets_profiles'
        verbose_name = 'User METS Profile'
        verbose_name_plural = 'User METS Profiles'
    
    def __str__(self):
        return f"{self.user.username} - {self.get_role_display()}"


class UserPermissionAssignment(models.Model):
    """
    Assignment of specific METS permissions to users
    """
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='mets_permissions')
    permission = models.ForeignKey(METSPermission, on_delete=models.CASCADE)
    
    # Context restrictions
    department_restriction = models.CharField(
        max_length=20, 
        choices=UserMETSProfile.DEPARTMENT_CHOICES, 
        blank=True,
        help_text="Restrict permission to specific department"
    )
    value_limit = models.DecimalField(
        max_digits=12, 
        decimal_places=2, 
        null=True, 
        blank=True,
        help_text="Maximum value for approval permissions"
    )
    
    # Validity
    valid_from = models.DateTimeField(auto_now_add=True)
    valid_until = models.DateTimeField(null=True, blank=True)
    is_active = models.BooleanField(default=True)
    
    # Audit
    assigned_by = models.ForeignKey(
        User, 
        on_delete=models.SET_NULL, 
        null=True, 
        related_name='permission_assignments_made'
    )
    assigned_at = models.DateTimeField(auto_now_add=True)
    notes = models.TextField(blank=True)
    
    class Meta:
        db_table = 'user_permission_assignments'
        verbose_name = 'User Permission Assignment'
        verbose_name_plural = 'User Permission Assignments'
        unique_together = ['user', 'permission', 'department_restriction']
        ordering = ['-assigned_at']
    
    def __str__(self):
        dept_str = f" ({self.department_restriction})" if self.department_restriction else ""
        return f"{self.user.username} - {self.permission.name}{dept_str}"
    
    def is_valid(self):
        """Check if permission assignment is currently valid"""
        if not self.is_active:
            return False
        
        if self.valid_until and self.valid_until < timezone.now():
            return False
        
        return True


class METSAuditLog(models.Model):
    """
    Audit logging for METS permission changes
    """
    ACTION_CHOICES = [
        ('assign', 'Assign Permission'),
        ('revoke', 'Revoke Permission'),
        ('modify', 'Modify Permission'),
        ('create', 'Create Permission'),
        ('delete', 'Delete Permission'),
    ]
    
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    action = models.CharField(max_length=20, choices=ACTION_CHOICES)
    permission = models.ForeignKey(METSPermission, on_delete=models.SET_NULL, null=True, blank=True)
    target_user = models.ForeignKey(
        User, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        related_name='permission_audit_logs'
    )
    
    # Context information
    ip_address = models.GenericIPAddressField(blank=True, null=True)
    details = models.JSONField(default=dict, help_text="Additional details about the action")
    
    timestamp = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'mets_audit_logs'
        verbose_name = 'METS Audit Log'
        verbose_name_plural = 'METS Audit Logs'
        ordering = ['-timestamp']
    
    def __str__(self):
        return f"{self.user} {self.action} {self.permission} at {self.timestamp}"
