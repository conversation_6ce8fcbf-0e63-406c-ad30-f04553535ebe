{"ast": null, "code": "import generateUtilityClass from '@mui/material/generateUtilityClass';\nimport generateUtilityClasses from '@mui/material/generateUtilityClasses';\nexport function getTimelineUtilityClass(slot) {\n  return generateUtilityClass('MuiTimeline', slot);\n}\nconst timelineClasses = generateUtilityClasses('MuiTimeline', ['root', 'positionLeft', 'positionRight', 'positionAlternate', 'positionAlternateReverse']);\nexport default timelineClasses;", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getTimelineUtilityClass", "slot", "timelineClasses"], "sources": ["C:/Users/<USER>/Desktop/Test_HTML/Material Managment1/frontend/node_modules/@mui/lab/Timeline/timelineClasses.js"], "sourcesContent": ["import generateUtilityClass from '@mui/material/generateUtilityClass';\nimport generateUtilityClasses from '@mui/material/generateUtilityClasses';\nexport function getTimelineUtilityClass(slot) {\n  return generateUtilityClass('MuiTimeline', slot);\n}\nconst timelineClasses = generateUtilityClasses('MuiTimeline', ['root', 'positionLeft', 'positionRight', 'positionAlternate', 'positionAlternateReverse']);\nexport default timelineClasses;"], "mappings": "AAAA,OAAOA,oBAAoB,MAAM,oCAAoC;AACrE,OAAOC,sBAAsB,MAAM,sCAAsC;AACzE,OAAO,SAASC,uBAAuBA,CAACC,IAAI,EAAE;EAC5C,OAAOH,oBAAoB,CAAC,aAAa,EAAEG,IAAI,CAAC;AAClD;AACA,MAAMC,eAAe,GAAGH,sBAAsB,CAAC,aAAa,EAAE,CAAC,MAAM,EAAE,cAAc,EAAE,eAAe,EAAE,mBAAmB,EAAE,0BAA0B,CAAC,CAAC;AACzJ,eAAeG,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}