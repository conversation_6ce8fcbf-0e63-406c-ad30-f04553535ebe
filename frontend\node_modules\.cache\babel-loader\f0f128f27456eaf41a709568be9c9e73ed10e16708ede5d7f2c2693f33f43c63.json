{"ast": null, "code": "import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"className\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport { styled, useThemeProps } from '@mui/material/styles';\nimport { unstable_composeClasses as composeClasses } from '@mui/base';\nimport Typography from '@mui/material/Typography';\nimport TimelineContext from '../Timeline/TimelineContext';\nimport { getTimelineContentUtilityClass } from './timelineContentClasses';\nimport convertTimelinePositionToClass from '../internal/convertTimelinePositionToClass';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    position,\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root', convertTimelinePositionToClass(position)]\n  };\n  return composeClasses(slots, getTimelineContentUtilityClass, classes);\n};\nconst TimelineContentRoot = styled(Typography, {\n  name: 'MuiTimelineContent',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[convertTimelinePositionToClass(ownerState.position)]];\n  }\n})(({\n  ownerState\n}) => _extends({\n  flex: 1,\n  padding: '6px 16px',\n  textAlign: 'left'\n}, ownerState.position === 'left' && {\n  textAlign: 'right'\n}));\nconst TimelineContent = /*#__PURE__*/React.forwardRef(function TimelineContent(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiTimelineContent'\n  });\n  const {\n      className\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    position: positionContext\n  } = React.useContext(TimelineContext);\n  const ownerState = _extends({}, props, {\n    position: positionContext || 'right'\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(TimelineContentRoot, _extends({\n    component: \"div\",\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    ref: ref\n  }, other));\n});\nprocess.env.NODE_ENV !== \"production\" ? TimelineContent.propTypes /* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit the d.ts file and run \"yarn proptypes\"     |\n  // ----------------------------------------------------------------------\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default TimelineContent;", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_excluded", "React", "PropTypes", "clsx", "styled", "useThemeProps", "unstable_composeClasses", "composeClasses", "Typography", "TimelineContext", "getTimelineContentUtilityClass", "convertTimelinePositionToClass", "jsx", "_jsx", "useUtilityClasses", "ownerState", "position", "classes", "slots", "root", "TimelineContentRoot", "name", "slot", "overridesResolver", "props", "styles", "flex", "padding", "textAlign", "TimelineContent", "forwardRef", "inProps", "ref", "className", "other", "positionContext", "useContext", "component", "process", "env", "NODE_ENV", "propTypes", "children", "node", "object", "string", "sx", "oneOfType", "arrayOf", "func", "bool"], "sources": ["C:/Users/<USER>/Desktop/Test_HTML/Material Managment1/frontend/node_modules/@mui/lab/TimelineContent/TimelineContent.js"], "sourcesContent": ["import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"className\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport { styled, useThemeProps } from '@mui/material/styles';\nimport { unstable_composeClasses as composeClasses } from '@mui/base';\nimport Typography from '@mui/material/Typography';\nimport TimelineContext from '../Timeline/TimelineContext';\nimport { getTimelineContentUtilityClass } from './timelineContentClasses';\nimport convertTimelinePositionToClass from '../internal/convertTimelinePositionToClass';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    position,\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root', convertTimelinePositionToClass(position)]\n  };\n  return composeClasses(slots, getTimelineContentUtilityClass, classes);\n};\nconst TimelineContentRoot = styled(Typography, {\n  name: 'MuiTimelineContent',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[convertTimelinePositionToClass(ownerState.position)]];\n  }\n})(({\n  ownerState\n}) => _extends({\n  flex: 1,\n  padding: '6px 16px',\n  textAlign: 'left'\n}, ownerState.position === 'left' && {\n  textAlign: 'right'\n}));\nconst TimelineContent = /*#__PURE__*/React.forwardRef(function TimelineContent(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiTimelineContent'\n  });\n  const {\n      className\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    position: positionContext\n  } = React.useContext(TimelineContext);\n  const ownerState = _extends({}, props, {\n    position: positionContext || 'right'\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(TimelineContentRoot, _extends({\n    component: \"div\",\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    ref: ref\n  }, other));\n});\nprocess.env.NODE_ENV !== \"production\" ? TimelineContent.propTypes /* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit the d.ts file and run \"yarn proptypes\"     |\n  // ----------------------------------------------------------------------\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default TimelineContent;"], "mappings": "AAAA,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,WAAW,CAAC;AAC/B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,SAASC,MAAM,EAAEC,aAAa,QAAQ,sBAAsB;AAC5D,SAASC,uBAAuB,IAAIC,cAAc,QAAQ,WAAW;AACrE,OAAOC,UAAU,MAAM,0BAA0B;AACjD,OAAOC,eAAe,MAAM,6BAA6B;AACzD,SAASC,8BAA8B,QAAQ,0BAA0B;AACzE,OAAOC,8BAA8B,MAAM,4CAA4C;AACvF,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,QAAQ;IACRC;EACF,CAAC,GAAGF,UAAU;EACd,MAAMG,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAER,8BAA8B,CAACK,QAAQ,CAAC;EACzD,CAAC;EACD,OAAOT,cAAc,CAACW,KAAK,EAAER,8BAA8B,EAAEO,OAAO,CAAC;AACvE,CAAC;AACD,MAAMG,mBAAmB,GAAGhB,MAAM,CAACI,UAAU,EAAE;EAC7Ca,IAAI,EAAE,oBAAoB;EAC1BC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJV;IACF,CAAC,GAAGS,KAAK;IACT,OAAO,CAACC,MAAM,CAACN,IAAI,EAAEM,MAAM,CAACd,8BAA8B,CAACI,UAAU,CAACC,QAAQ,CAAC,CAAC,CAAC;EACnF;AACF,CAAC,CAAC,CAAC,CAAC;EACFD;AACF,CAAC,KAAKhB,QAAQ,CAAC;EACb2B,IAAI,EAAE,CAAC;EACPC,OAAO,EAAE,UAAU;EACnBC,SAAS,EAAE;AACb,CAAC,EAAEb,UAAU,CAACC,QAAQ,KAAK,MAAM,IAAI;EACnCY,SAAS,EAAE;AACb,CAAC,CAAC,CAAC;AACH,MAAMC,eAAe,GAAG,aAAa5B,KAAK,CAAC6B,UAAU,CAAC,SAASD,eAAeA,CAACE,OAAO,EAAEC,GAAG,EAAE;EAC3F,MAAMR,KAAK,GAAGnB,aAAa,CAAC;IAC1BmB,KAAK,EAAEO,OAAO;IACdV,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;MACFY;IACF,CAAC,GAAGT,KAAK;IACTU,KAAK,GAAGpC,6BAA6B,CAAC0B,KAAK,EAAExB,SAAS,CAAC;EACzD,MAAM;IACJgB,QAAQ,EAAEmB;EACZ,CAAC,GAAGlC,KAAK,CAACmC,UAAU,CAAC3B,eAAe,CAAC;EACrC,MAAMM,UAAU,GAAGhB,QAAQ,CAAC,CAAC,CAAC,EAAEyB,KAAK,EAAE;IACrCR,QAAQ,EAAEmB,eAAe,IAAI;EAC/B,CAAC,CAAC;EACF,MAAMlB,OAAO,GAAGH,iBAAiB,CAACC,UAAU,CAAC;EAC7C,OAAO,aAAaF,IAAI,CAACO,mBAAmB,EAAErB,QAAQ,CAAC;IACrDsC,SAAS,EAAE,KAAK;IAChBJ,SAAS,EAAE9B,IAAI,CAACc,OAAO,CAACE,IAAI,EAAEc,SAAS,CAAC;IACxClB,UAAU,EAAEA,UAAU;IACtBiB,GAAG,EAAEA;EACP,CAAC,EAAEE,KAAK,CAAC,CAAC;AACZ,CAAC,CAAC;AACFI,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGX,eAAe,CAACY,SAAS,CAAC,yBAAyB;EACzF;EACA;EACA;EACA;EACA;AACF;AACA;EACEC,QAAQ,EAAExC,SAAS,CAACyC,IAAI;EACxB;AACF;AACA;EACE1B,OAAO,EAAEf,SAAS,CAAC0C,MAAM;EACzB;AACF;AACA;EACEX,SAAS,EAAE/B,SAAS,CAAC2C,MAAM;EAC3B;AACF;AACA;EACEC,EAAE,EAAE5C,SAAS,CAAC6C,SAAS,CAAC,CAAC7C,SAAS,CAAC8C,OAAO,CAAC9C,SAAS,CAAC6C,SAAS,CAAC,CAAC7C,SAAS,CAAC+C,IAAI,EAAE/C,SAAS,CAAC0C,MAAM,EAAE1C,SAAS,CAACgD,IAAI,CAAC,CAAC,CAAC,EAAEhD,SAAS,CAAC+C,IAAI,EAAE/C,SAAS,CAAC0C,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACV,eAAef,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}