from rest_framework import serializers
from .models import Attachment

class AttachmentSerializer(serializers.ModelSerializer):
    """Serializer for file attachments"""
    class Meta:
        model = Attachment
        fields = ['id', 'file', 'original_filename', 'file_size', 'content_type', 'uploaded_at', 'uploaded_by']
        read_only_fields = ['id', 'original_filename', 'file_size', 'content_type', 'uploaded_at', 'uploaded_by']

    def validate_file(self, value):
        """Validate the uploaded file"""
        # Add file size validation (e.g., max 10MB)
        max_size = 10 * 1024 * 1024
        if value.size > max_size:
            raise serializers.ValidationError(f"File size exceeds {max_size} bytes limit.")
        
        # Add file type validation if needed
        # allowed_types = ['image/jpeg', 'application/pdf']
        # if value.content_type not in allowed_types:
        #     raise serializers.ValidationError("File type not allowed.")
        
        return value