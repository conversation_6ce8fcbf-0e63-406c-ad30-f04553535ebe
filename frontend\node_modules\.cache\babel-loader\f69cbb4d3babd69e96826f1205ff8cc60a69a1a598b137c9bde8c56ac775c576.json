{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport { styled } from '@mui/system';\nimport { unstable_composeClasses as composeClasses } from '@mui/utils';\nimport { useGridRootProps } from '../../hooks/utils/useGridRootProps';\nimport { getDataGridUtilityClass } from '../../constants/gridClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['virtualScroller']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, classes);\n};\nconst VirtualScrollerRoot = styled('div', {\n  name: 'MuiDataGrid',\n  slot: 'VirtualScroller',\n  overridesResolver: (props, styles) => styles.virtualScroller\n})({\n  overflow: 'auto',\n  height: '100%',\n  // See https://github.com/mui/mui-x/issues/4360\n  position: 'relative',\n  '@media print': {\n    overflow: 'hidden'\n  },\n  zIndex: 0 // See https://github.com/mui/mui-x/issues/10547\n});\nconst GridVirtualScroller = /*#__PURE__*/React.forwardRef(function GridVirtualScroller(props, ref) {\n  const rootProps = useGridRootProps();\n  const classes = useUtilityClasses(rootProps);\n  return /*#__PURE__*/_jsx(VirtualScrollerRoot, _extends({\n    ref: ref\n  }, props, {\n    className: clsx(classes.root, props.className),\n    ownerState: rootProps\n  }));\n});\nexport { GridVirtualScroller };", "map": {"version": 3, "names": ["_extends", "React", "clsx", "styled", "unstable_composeClasses", "composeClasses", "useGridRootProps", "getDataGridUtilityClass", "jsx", "_jsx", "useUtilityClasses", "ownerState", "classes", "slots", "root", "VirtualScrollerRoot", "name", "slot", "overridesResolver", "props", "styles", "virtualScroller", "overflow", "height", "position", "zIndex", "GridVirtualScroller", "forwardRef", "ref", "rootProps", "className"], "sources": ["C:/Users/<USER>/Desktop/Test_HTML/Material Managment1/frontend/node_modules/@mui/x-data-grid/components/virtualization/GridVirtualScroller.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport { styled } from '@mui/system';\nimport { unstable_composeClasses as composeClasses } from '@mui/utils';\nimport { useGridRootProps } from '../../hooks/utils/useGridRootProps';\nimport { getDataGridUtilityClass } from '../../constants/gridClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['virtualScroller']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, classes);\n};\nconst VirtualScrollerRoot = styled('div', {\n  name: 'MuiDataGrid',\n  slot: 'VirtualScroller',\n  overridesResolver: (props, styles) => styles.virtualScroller\n})({\n  overflow: 'auto',\n  height: '100%',\n  // See https://github.com/mui/mui-x/issues/4360\n  position: 'relative',\n  '@media print': {\n    overflow: 'hidden'\n  },\n  zIndex: 0 // See https://github.com/mui/mui-x/issues/10547\n});\nconst GridVirtualScroller = /*#__PURE__*/React.forwardRef(function GridVirtualScroller(props, ref) {\n  const rootProps = useGridRootProps();\n  const classes = useUtilityClasses(rootProps);\n  return /*#__PURE__*/_jsx(VirtualScrollerRoot, _extends({\n    ref: ref\n  }, props, {\n    className: clsx(classes.root, props.className),\n    ownerState: rootProps\n  }));\n});\nexport { GridVirtualScroller };"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,IAAI,MAAM,MAAM;AACvB,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,uBAAuB,IAAIC,cAAc,QAAQ,YAAY;AACtE,SAASC,gBAAgB,QAAQ,oCAAoC;AACrE,SAASC,uBAAuB,QAAQ,6BAA6B;AACrE,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC;EACF,CAAC,GAAGD,UAAU;EACd,MAAME,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,iBAAiB;EAC1B,CAAC;EACD,OAAOT,cAAc,CAACQ,KAAK,EAAEN,uBAAuB,EAAEK,OAAO,CAAC;AAChE,CAAC;AACD,MAAMG,mBAAmB,GAAGZ,MAAM,CAAC,KAAK,EAAE;EACxCa,IAAI,EAAE,aAAa;EACnBC,IAAI,EAAE,iBAAiB;EACvBC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACC;AAC/C,CAAC,CAAC,CAAC;EACDC,QAAQ,EAAE,MAAM;EAChBC,MAAM,EAAE,MAAM;EACd;EACAC,QAAQ,EAAE,UAAU;EACpB,cAAc,EAAE;IACdF,QAAQ,EAAE;EACZ,CAAC;EACDG,MAAM,EAAE,CAAC,CAAC;AACZ,CAAC,CAAC;AACF,MAAMC,mBAAmB,GAAG,aAAazB,KAAK,CAAC0B,UAAU,CAAC,SAASD,mBAAmBA,CAACP,KAAK,EAAES,GAAG,EAAE;EACjG,MAAMC,SAAS,GAAGvB,gBAAgB,CAAC,CAAC;EACpC,MAAMM,OAAO,GAAGF,iBAAiB,CAACmB,SAAS,CAAC;EAC5C,OAAO,aAAapB,IAAI,CAACM,mBAAmB,EAAEf,QAAQ,CAAC;IACrD4B,GAAG,EAAEA;EACP,CAAC,EAAET,KAAK,EAAE;IACRW,SAAS,EAAE5B,IAAI,CAACU,OAAO,CAACE,IAAI,EAAEK,KAAK,CAACW,SAAS,CAAC;IAC9CnB,UAAU,EAAEkB;EACd,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACF,SAASH,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}