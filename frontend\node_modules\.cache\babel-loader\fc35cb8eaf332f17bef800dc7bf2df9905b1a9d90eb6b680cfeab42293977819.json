{"ast": null, "code": "import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"displayOrder\"];\nimport * as React from 'react';\nimport Divider from '@mui/material/Divider';\nimport { useGridPrivateApiContext } from '../../utils/useGridPrivateApiContext';\nconst useGridColumnMenuSlots = props => {\n  const apiRef = useGridPrivateApiContext();\n  const {\n    defaultSlots,\n    defaultSlotProps,\n    slots = {},\n    slotProps = {},\n    hideMenu,\n    colDef,\n    addDividers = true\n  } = props;\n  const processedComponents = React.useMemo(() => _extends({}, defaultSlots, slots), [defaultSlots, slots]);\n  const processedSlotProps = React.useMemo(() => {\n    if (!slotProps || Object.keys(slotProps).length === 0) {\n      return defaultSlotProps;\n    }\n    const mergedProps = _extends({}, slotProps);\n    Object.entries(defaultSlotProps).forEach(([key, currentSlotProps]) => {\n      mergedProps[key] = _extends({}, currentSlotProps, slotProps[key] || {});\n    });\n    return mergedProps;\n  }, [defaultSlotProps, slotProps]);\n  const defaultItems = apiRef.current.unstable_applyPipeProcessors('columnMenu', [], props.colDef);\n  const userItems = React.useMemo(() => {\n    const defaultComponentKeys = Object.keys(defaultSlots);\n    return Object.keys(slots).filter(key => !defaultComponentKeys.includes(key));\n  }, [slots, defaultSlots]);\n  return React.useMemo(() => {\n    const uniqueItems = Array.from(new Set([...defaultItems, ...userItems]));\n    const cleansedItems = uniqueItems.filter(key => processedComponents[key] != null);\n    const sorted = cleansedItems.sort((a, b) => {\n      const leftItemProps = processedSlotProps[a];\n      const rightItemProps = processedSlotProps[b];\n      const leftDisplayOrder = Number.isFinite(leftItemProps == null ? void 0 : leftItemProps.displayOrder) ? leftItemProps.displayOrder : 100;\n      const rightDisplayOrder = Number.isFinite(rightItemProps == null ? void 0 : rightItemProps.displayOrder) ? rightItemProps.displayOrder : 100;\n      return leftDisplayOrder - rightDisplayOrder;\n    });\n    return sorted.reduce((acc, key, index) => {\n      let itemProps = {\n        colDef,\n        onClick: hideMenu\n      };\n      const processedComponentProps = processedSlotProps[key];\n      if (processedComponentProps) {\n        const customProps = _objectWithoutPropertiesLoose(processedComponentProps, _excluded);\n        itemProps = _extends({}, itemProps, customProps);\n      }\n      return addDividers && index !== sorted.length - 1 ? [...acc, [processedComponents[key], itemProps], [Divider, {}]] : [...acc, [processedComponents[key], itemProps]];\n    }, []);\n  }, [addDividers, colDef, defaultItems, hideMenu, processedComponents, processedSlotProps, userItems]);\n};\nexport { useGridColumnMenuSlots };", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_excluded", "React", "Divider", "useGridPrivateApiContext", "useGridColumnMenuSlots", "props", "apiRef", "defaultSlots", "defaultSlotProps", "slots", "slotProps", "hideMenu", "colDef", "addDividers", "processedComponents", "useMemo", "processedSlotProps", "Object", "keys", "length", "mergedProps", "entries", "for<PERSON>ach", "key", "currentSlotProps", "defaultItems", "current", "unstable_applyPipeProcessors", "userItems", "defaultComponentKeys", "filter", "includes", "uniqueItems", "Array", "from", "Set", "cleansedItems", "sorted", "sort", "a", "b", "leftItemProps", "rightItemProps", "leftDisplayOrder", "Number", "isFinite", "displayOrder", "rightDisplayOrder", "reduce", "acc", "index", "itemProps", "onClick", "processedComponentProps", "customProps"], "sources": ["C:/Users/<USER>/Desktop/Test_HTML/Material Managment1/frontend/node_modules/@mui/x-data-grid/hooks/features/columnMenu/useGridColumnMenuSlots.js"], "sourcesContent": ["import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"displayOrder\"];\nimport * as React from 'react';\nimport Divider from '@mui/material/Divider';\nimport { useGridPrivateApiContext } from '../../utils/useGridPrivateApiContext';\nconst useGridColumnMenuSlots = props => {\n  const apiRef = useGridPrivateApiContext();\n  const {\n    defaultSlots,\n    defaultSlotProps,\n    slots = {},\n    slotProps = {},\n    hideMenu,\n    colDef,\n    addDividers = true\n  } = props;\n  const processedComponents = React.useMemo(() => _extends({}, defaultSlots, slots), [defaultSlots, slots]);\n  const processedSlotProps = React.useMemo(() => {\n    if (!slotProps || Object.keys(slotProps).length === 0) {\n      return defaultSlotProps;\n    }\n    const mergedProps = _extends({}, slotProps);\n    Object.entries(defaultSlotProps).forEach(([key, currentSlotProps]) => {\n      mergedProps[key] = _extends({}, currentSlotProps, slotProps[key] || {});\n    });\n    return mergedProps;\n  }, [defaultSlotProps, slotProps]);\n  const defaultItems = apiRef.current.unstable_applyPipeProcessors('columnMenu', [], props.colDef);\n  const userItems = React.useMemo(() => {\n    const defaultComponentKeys = Object.keys(defaultSlots);\n    return Object.keys(slots).filter(key => !defaultComponentKeys.includes(key));\n  }, [slots, defaultSlots]);\n  return React.useMemo(() => {\n    const uniqueItems = Array.from(new Set([...defaultItems, ...userItems]));\n    const cleansedItems = uniqueItems.filter(key => processedComponents[key] != null);\n    const sorted = cleansedItems.sort((a, b) => {\n      const leftItemProps = processedSlotProps[a];\n      const rightItemProps = processedSlotProps[b];\n      const leftDisplayOrder = Number.isFinite(leftItemProps == null ? void 0 : leftItemProps.displayOrder) ? leftItemProps.displayOrder : 100;\n      const rightDisplayOrder = Number.isFinite(rightItemProps == null ? void 0 : rightItemProps.displayOrder) ? rightItemProps.displayOrder : 100;\n      return leftDisplayOrder - rightDisplayOrder;\n    });\n    return sorted.reduce((acc, key, index) => {\n      let itemProps = {\n        colDef,\n        onClick: hideMenu\n      };\n      const processedComponentProps = processedSlotProps[key];\n      if (processedComponentProps) {\n        const customProps = _objectWithoutPropertiesLoose(processedComponentProps, _excluded);\n        itemProps = _extends({}, itemProps, customProps);\n      }\n      return addDividers && index !== sorted.length - 1 ? [...acc, [processedComponents[key], itemProps], [Divider, {}]] : [...acc, [processedComponents[key], itemProps]];\n    }, []);\n  }, [addDividers, colDef, defaultItems, hideMenu, processedComponents, processedSlotProps, userItems]);\n};\nexport { useGridColumnMenuSlots };"], "mappings": "AAAA,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,cAAc,CAAC;AAClC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,OAAO,MAAM,uBAAuB;AAC3C,SAASC,wBAAwB,QAAQ,sCAAsC;AAC/E,MAAMC,sBAAsB,GAAGC,KAAK,IAAI;EACtC,MAAMC,MAAM,GAAGH,wBAAwB,CAAC,CAAC;EACzC,MAAM;IACJI,YAAY;IACZC,gBAAgB;IAChBC,KAAK,GAAG,CAAC,CAAC;IACVC,SAAS,GAAG,CAAC,CAAC;IACdC,QAAQ;IACRC,MAAM;IACNC,WAAW,GAAG;EAChB,CAAC,GAAGR,KAAK;EACT,MAAMS,mBAAmB,GAAGb,KAAK,CAACc,OAAO,CAAC,MAAMhB,QAAQ,CAAC,CAAC,CAAC,EAAEQ,YAAY,EAAEE,KAAK,CAAC,EAAE,CAACF,YAAY,EAAEE,KAAK,CAAC,CAAC;EACzG,MAAMO,kBAAkB,GAAGf,KAAK,CAACc,OAAO,CAAC,MAAM;IAC7C,IAAI,CAACL,SAAS,IAAIO,MAAM,CAACC,IAAI,CAACR,SAAS,CAAC,CAACS,MAAM,KAAK,CAAC,EAAE;MACrD,OAAOX,gBAAgB;IACzB;IACA,MAAMY,WAAW,GAAGrB,QAAQ,CAAC,CAAC,CAAC,EAAEW,SAAS,CAAC;IAC3CO,MAAM,CAACI,OAAO,CAACb,gBAAgB,CAAC,CAACc,OAAO,CAAC,CAAC,CAACC,GAAG,EAAEC,gBAAgB,CAAC,KAAK;MACpEJ,WAAW,CAACG,GAAG,CAAC,GAAGxB,QAAQ,CAAC,CAAC,CAAC,EAAEyB,gBAAgB,EAAEd,SAAS,CAACa,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;IACzE,CAAC,CAAC;IACF,OAAOH,WAAW;EACpB,CAAC,EAAE,CAACZ,gBAAgB,EAAEE,SAAS,CAAC,CAAC;EACjC,MAAMe,YAAY,GAAGnB,MAAM,CAACoB,OAAO,CAACC,4BAA4B,CAAC,YAAY,EAAE,EAAE,EAAEtB,KAAK,CAACO,MAAM,CAAC;EAChG,MAAMgB,SAAS,GAAG3B,KAAK,CAACc,OAAO,CAAC,MAAM;IACpC,MAAMc,oBAAoB,GAAGZ,MAAM,CAACC,IAAI,CAACX,YAAY,CAAC;IACtD,OAAOU,MAAM,CAACC,IAAI,CAACT,KAAK,CAAC,CAACqB,MAAM,CAACP,GAAG,IAAI,CAACM,oBAAoB,CAACE,QAAQ,CAACR,GAAG,CAAC,CAAC;EAC9E,CAAC,EAAE,CAACd,KAAK,EAAEF,YAAY,CAAC,CAAC;EACzB,OAAON,KAAK,CAACc,OAAO,CAAC,MAAM;IACzB,MAAMiB,WAAW,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAIC,GAAG,CAAC,CAAC,GAAGV,YAAY,EAAE,GAAGG,SAAS,CAAC,CAAC,CAAC;IACxE,MAAMQ,aAAa,GAAGJ,WAAW,CAACF,MAAM,CAACP,GAAG,IAAIT,mBAAmB,CAACS,GAAG,CAAC,IAAI,IAAI,CAAC;IACjF,MAAMc,MAAM,GAAGD,aAAa,CAACE,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MAC1C,MAAMC,aAAa,GAAGzB,kBAAkB,CAACuB,CAAC,CAAC;MAC3C,MAAMG,cAAc,GAAG1B,kBAAkB,CAACwB,CAAC,CAAC;MAC5C,MAAMG,gBAAgB,GAAGC,MAAM,CAACC,QAAQ,CAACJ,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACK,YAAY,CAAC,GAAGL,aAAa,CAACK,YAAY,GAAG,GAAG;MACxI,MAAMC,iBAAiB,GAAGH,MAAM,CAACC,QAAQ,CAACH,cAAc,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACI,YAAY,CAAC,GAAGJ,cAAc,CAACI,YAAY,GAAG,GAAG;MAC5I,OAAOH,gBAAgB,GAAGI,iBAAiB;IAC7C,CAAC,CAAC;IACF,OAAOV,MAAM,CAACW,MAAM,CAAC,CAACC,GAAG,EAAE1B,GAAG,EAAE2B,KAAK,KAAK;MACxC,IAAIC,SAAS,GAAG;QACdvC,MAAM;QACNwC,OAAO,EAAEzC;MACX,CAAC;MACD,MAAM0C,uBAAuB,GAAGrC,kBAAkB,CAACO,GAAG,CAAC;MACvD,IAAI8B,uBAAuB,EAAE;QAC3B,MAAMC,WAAW,GAAGxD,6BAA6B,CAACuD,uBAAuB,EAAErD,SAAS,CAAC;QACrFmD,SAAS,GAAGpD,QAAQ,CAAC,CAAC,CAAC,EAAEoD,SAAS,EAAEG,WAAW,CAAC;MAClD;MACA,OAAOzC,WAAW,IAAIqC,KAAK,KAAKb,MAAM,CAAClB,MAAM,GAAG,CAAC,GAAG,CAAC,GAAG8B,GAAG,EAAE,CAACnC,mBAAmB,CAACS,GAAG,CAAC,EAAE4B,SAAS,CAAC,EAAE,CAACjD,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG+C,GAAG,EAAE,CAACnC,mBAAmB,CAACS,GAAG,CAAC,EAAE4B,SAAS,CAAC,CAAC;IACtK,CAAC,EAAE,EAAE,CAAC;EACR,CAAC,EAAE,CAACtC,WAAW,EAAED,MAAM,EAAEa,YAAY,EAAEd,QAAQ,EAAEG,mBAAmB,EAAEE,kBAAkB,EAAEY,SAAS,CAAC,CAAC;AACvG,CAAC;AACD,SAASxB,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}