{"ast": null, "code": "import * as React from 'react';\nimport { GridApiContext } from '../components/GridApiContext';\nimport { GridPrivateApiContext } from '../hooks/utils/useGridPrivateApiContext';\nimport { GridRootPropsContext } from './GridRootPropsContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport function GridContextProvider({\n  privateApiRef,\n  props,\n  children\n}) {\n  const apiRef = React.useRef(privateApiRef.current.getPublicApi());\n  return /*#__PURE__*/_jsx(GridRootPropsContext.Provider, {\n    value: props,\n    children: /*#__PURE__*/_jsx(GridPrivateApiContext.Provider, {\n      value: privateApiRef,\n      children: /*#__PURE__*/_jsx(GridApiContext.Provider, {\n        value: apiRef,\n        children: children\n      })\n    })\n  });\n}", "map": {"version": 3, "names": ["React", "GridApiContext", "GridPrivateApiContext", "GridRootPropsContext", "jsx", "_jsx", "GridContextProvider", "privateApiRef", "props", "children", "apiRef", "useRef", "current", "getPublicApi", "Provider", "value"], "sources": ["C:/Users/<USER>/Desktop/Test_HTML/Material Managment1/frontend/node_modules/@mui/x-data-grid/context/GridContextProvider.js"], "sourcesContent": ["import * as React from 'react';\nimport { GridApiContext } from '../components/GridApiContext';\nimport { GridPrivateApiContext } from '../hooks/utils/useGridPrivateApiContext';\nimport { GridRootPropsContext } from './GridRootPropsContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport function GridContextProvider({\n  privateApiRef,\n  props,\n  children\n}) {\n  const apiRef = React.useRef(privateApiRef.current.getPublicApi());\n  return /*#__PURE__*/_jsx(GridRootPropsContext.Provider, {\n    value: props,\n    children: /*#__PURE__*/_jsx(GridPrivateApiContext.Provider, {\n      value: privateApiRef,\n      children: /*#__PURE__*/_jsx(GridApiContext.Provider, {\n        value: apiRef,\n        children: children\n      })\n    })\n  });\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,cAAc,QAAQ,8BAA8B;AAC7D,SAASC,qBAAqB,QAAQ,yCAAyC;AAC/E,SAASC,oBAAoB,QAAQ,wBAAwB;AAC7D,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,OAAO,SAASC,mBAAmBA,CAAC;EAClCC,aAAa;EACbC,KAAK;EACLC;AACF,CAAC,EAAE;EACD,MAAMC,MAAM,GAAGV,KAAK,CAACW,MAAM,CAACJ,aAAa,CAACK,OAAO,CAACC,YAAY,CAAC,CAAC,CAAC;EACjE,OAAO,aAAaR,IAAI,CAACF,oBAAoB,CAACW,QAAQ,EAAE;IACtDC,KAAK,EAAEP,KAAK;IACZC,QAAQ,EAAE,aAAaJ,IAAI,CAACH,qBAAqB,CAACY,QAAQ,EAAE;MAC1DC,KAAK,EAAER,aAAa;MACpBE,QAAQ,EAAE,aAAaJ,IAAI,CAACJ,cAAc,CAACa,QAAQ,EAAE;QACnDC,KAAK,EAAEL,MAAM;QACbD,QAAQ,EAAEA;MACZ,CAAC;IACH,CAAC;EACH,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}