{"ast": null, "code": "import _inherits<PERSON><PERSON>e from \"@babel/runtime/helpers/esm/inheritsLoose\";\nimport { notifyManager } from './notifyManager';\nimport { Mutation } from './mutation';\nimport { matchMutation, noop } from './utils';\nimport { Subscribable } from './subscribable'; // TYPES\n\n// CLASS\nexport var MutationCache = /*#__PURE__*/function (_Subscribable) {\n  _inheritsLoose(MutationCache, _Subscribable);\n  function MutationCache(config) {\n    var _this;\n    _this = _Subscribable.call(this) || this;\n    _this.config = config || {};\n    _this.mutations = [];\n    _this.mutationId = 0;\n    return _this;\n  }\n  var _proto = MutationCache.prototype;\n  _proto.build = function build(client, options, state) {\n    var mutation = new Mutation({\n      mutationCache: this,\n      mutationId: ++this.mutationId,\n      options: client.defaultMutationOptions(options),\n      state: state,\n      defaultOptions: options.mutationKey ? client.getMutationDefaults(options.mutationKey) : undefined,\n      meta: options.meta\n    });\n    this.add(mutation);\n    return mutation;\n  };\n  _proto.add = function add(mutation) {\n    this.mutations.push(mutation);\n    this.notify(mutation);\n  };\n  _proto.remove = function remove(mutation) {\n    this.mutations = this.mutations.filter(function (x) {\n      return x !== mutation;\n    });\n    mutation.cancel();\n    this.notify(mutation);\n  };\n  _proto.clear = function clear() {\n    var _this2 = this;\n    notifyManager.batch(function () {\n      _this2.mutations.forEach(function (mutation) {\n        _this2.remove(mutation);\n      });\n    });\n  };\n  _proto.getAll = function getAll() {\n    return this.mutations;\n  };\n  _proto.find = function find(filters) {\n    if (typeof filters.exact === 'undefined') {\n      filters.exact = true;\n    }\n    return this.mutations.find(function (mutation) {\n      return matchMutation(filters, mutation);\n    });\n  };\n  _proto.findAll = function findAll(filters) {\n    return this.mutations.filter(function (mutation) {\n      return matchMutation(filters, mutation);\n    });\n  };\n  _proto.notify = function notify(mutation) {\n    var _this3 = this;\n    notifyManager.batch(function () {\n      _this3.listeners.forEach(function (listener) {\n        listener(mutation);\n      });\n    });\n  };\n  _proto.onFocus = function onFocus() {\n    this.resumePausedMutations();\n  };\n  _proto.onOnline = function onOnline() {\n    this.resumePausedMutations();\n  };\n  _proto.resumePausedMutations = function resumePausedMutations() {\n    var pausedMutations = this.mutations.filter(function (x) {\n      return x.state.isPaused;\n    });\n    return notifyManager.batch(function () {\n      return pausedMutations.reduce(function (promise, mutation) {\n        return promise.then(function () {\n          return mutation.continue().catch(noop);\n        });\n      }, Promise.resolve());\n    });\n  };\n  return MutationCache;\n}(Subscribable);", "map": {"version": 3, "names": ["_inherits<PERSON><PERSON>e", "notify<PERSON><PERSON>ger", "Mutation", "matchMutation", "noop", "Subscribable", "MutationCache", "_Subscribable", "config", "_this", "call", "mutations", "mutationId", "_proto", "prototype", "build", "client", "options", "state", "mutation", "mutationCache", "defaultMutationOptions", "defaultOptions", "<PERSON><PERSON><PERSON>", "getMutationDefaults", "undefined", "meta", "add", "push", "notify", "remove", "filter", "x", "cancel", "clear", "_this2", "batch", "for<PERSON>ach", "getAll", "find", "filters", "exact", "findAll", "_this3", "listeners", "listener", "onFocus", "resumePausedMutations", "onOnline", "pausedMutations", "isPaused", "reduce", "promise", "then", "continue", "catch", "Promise", "resolve"], "sources": ["C:/Users/<USER>/Desktop/Test_HTML/Material Managment1/frontend/node_modules/react-query/es/core/mutationCache.js"], "sourcesContent": ["import _inherits<PERSON><PERSON>e from \"@babel/runtime/helpers/esm/inheritsLoose\";\nimport { notifyManager } from './notifyManager';\nimport { Mutation } from './mutation';\nimport { matchMutation, noop } from './utils';\nimport { Subscribable } from './subscribable'; // TYPES\n\n// CLASS\nexport var MutationCache = /*#__PURE__*/function (_Subscribable) {\n  _inheritsLoose(MutationCache, _Subscribable);\n\n  function MutationCache(config) {\n    var _this;\n\n    _this = _Subscribable.call(this) || this;\n    _this.config = config || {};\n    _this.mutations = [];\n    _this.mutationId = 0;\n    return _this;\n  }\n\n  var _proto = MutationCache.prototype;\n\n  _proto.build = function build(client, options, state) {\n    var mutation = new Mutation({\n      mutationCache: this,\n      mutationId: ++this.mutationId,\n      options: client.defaultMutationOptions(options),\n      state: state,\n      defaultOptions: options.mutationKey ? client.getMutationDefaults(options.mutationKey) : undefined,\n      meta: options.meta\n    });\n    this.add(mutation);\n    return mutation;\n  };\n\n  _proto.add = function add(mutation) {\n    this.mutations.push(mutation);\n    this.notify(mutation);\n  };\n\n  _proto.remove = function remove(mutation) {\n    this.mutations = this.mutations.filter(function (x) {\n      return x !== mutation;\n    });\n    mutation.cancel();\n    this.notify(mutation);\n  };\n\n  _proto.clear = function clear() {\n    var _this2 = this;\n\n    notifyManager.batch(function () {\n      _this2.mutations.forEach(function (mutation) {\n        _this2.remove(mutation);\n      });\n    });\n  };\n\n  _proto.getAll = function getAll() {\n    return this.mutations;\n  };\n\n  _proto.find = function find(filters) {\n    if (typeof filters.exact === 'undefined') {\n      filters.exact = true;\n    }\n\n    return this.mutations.find(function (mutation) {\n      return matchMutation(filters, mutation);\n    });\n  };\n\n  _proto.findAll = function findAll(filters) {\n    return this.mutations.filter(function (mutation) {\n      return matchMutation(filters, mutation);\n    });\n  };\n\n  _proto.notify = function notify(mutation) {\n    var _this3 = this;\n\n    notifyManager.batch(function () {\n      _this3.listeners.forEach(function (listener) {\n        listener(mutation);\n      });\n    });\n  };\n\n  _proto.onFocus = function onFocus() {\n    this.resumePausedMutations();\n  };\n\n  _proto.onOnline = function onOnline() {\n    this.resumePausedMutations();\n  };\n\n  _proto.resumePausedMutations = function resumePausedMutations() {\n    var pausedMutations = this.mutations.filter(function (x) {\n      return x.state.isPaused;\n    });\n    return notifyManager.batch(function () {\n      return pausedMutations.reduce(function (promise, mutation) {\n        return promise.then(function () {\n          return mutation.continue().catch(noop);\n        });\n      }, Promise.resolve());\n    });\n  };\n\n  return MutationCache;\n}(Subscribable);"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,QAAQ,QAAQ,YAAY;AACrC,SAASC,aAAa,EAAEC,IAAI,QAAQ,SAAS;AAC7C,SAASC,YAAY,QAAQ,gBAAgB,CAAC,CAAC;;AAE/C;AACA,OAAO,IAAIC,aAAa,GAAG,aAAa,UAAUC,aAAa,EAAE;EAC/DP,cAAc,CAACM,aAAa,EAAEC,aAAa,CAAC;EAE5C,SAASD,aAAaA,CAACE,MAAM,EAAE;IAC7B,IAAIC,KAAK;IAETA,KAAK,GAAGF,aAAa,CAACG,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;IACxCD,KAAK,CAACD,MAAM,GAAGA,MAAM,IAAI,CAAC,CAAC;IAC3BC,KAAK,CAACE,SAAS,GAAG,EAAE;IACpBF,KAAK,CAACG,UAAU,GAAG,CAAC;IACpB,OAAOH,KAAK;EACd;EAEA,IAAII,MAAM,GAAGP,aAAa,CAACQ,SAAS;EAEpCD,MAAM,CAACE,KAAK,GAAG,SAASA,KAAKA,CAACC,MAAM,EAAEC,OAAO,EAAEC,KAAK,EAAE;IACpD,IAAIC,QAAQ,GAAG,IAAIjB,QAAQ,CAAC;MAC1BkB,aAAa,EAAE,IAAI;MACnBR,UAAU,EAAE,EAAE,IAAI,CAACA,UAAU;MAC7BK,OAAO,EAAED,MAAM,CAACK,sBAAsB,CAACJ,OAAO,CAAC;MAC/CC,KAAK,EAAEA,KAAK;MACZI,cAAc,EAAEL,OAAO,CAACM,WAAW,GAAGP,MAAM,CAACQ,mBAAmB,CAACP,OAAO,CAACM,WAAW,CAAC,GAAGE,SAAS;MACjGC,IAAI,EAAET,OAAO,CAACS;IAChB,CAAC,CAAC;IACF,IAAI,CAACC,GAAG,CAACR,QAAQ,CAAC;IAClB,OAAOA,QAAQ;EACjB,CAAC;EAEDN,MAAM,CAACc,GAAG,GAAG,SAASA,GAAGA,CAACR,QAAQ,EAAE;IAClC,IAAI,CAACR,SAAS,CAACiB,IAAI,CAACT,QAAQ,CAAC;IAC7B,IAAI,CAACU,MAAM,CAACV,QAAQ,CAAC;EACvB,CAAC;EAEDN,MAAM,CAACiB,MAAM,GAAG,SAASA,MAAMA,CAACX,QAAQ,EAAE;IACxC,IAAI,CAACR,SAAS,GAAG,IAAI,CAACA,SAAS,CAACoB,MAAM,CAAC,UAAUC,CAAC,EAAE;MAClD,OAAOA,CAAC,KAAKb,QAAQ;IACvB,CAAC,CAAC;IACFA,QAAQ,CAACc,MAAM,CAAC,CAAC;IACjB,IAAI,CAACJ,MAAM,CAACV,QAAQ,CAAC;EACvB,CAAC;EAEDN,MAAM,CAACqB,KAAK,GAAG,SAASA,KAAKA,CAAA,EAAG;IAC9B,IAAIC,MAAM,GAAG,IAAI;IAEjBlC,aAAa,CAACmC,KAAK,CAAC,YAAY;MAC9BD,MAAM,CAACxB,SAAS,CAAC0B,OAAO,CAAC,UAAUlB,QAAQ,EAAE;QAC3CgB,MAAM,CAACL,MAAM,CAACX,QAAQ,CAAC;MACzB,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;EAEDN,MAAM,CAACyB,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;IAChC,OAAO,IAAI,CAAC3B,SAAS;EACvB,CAAC;EAEDE,MAAM,CAAC0B,IAAI,GAAG,SAASA,IAAIA,CAACC,OAAO,EAAE;IACnC,IAAI,OAAOA,OAAO,CAACC,KAAK,KAAK,WAAW,EAAE;MACxCD,OAAO,CAACC,KAAK,GAAG,IAAI;IACtB;IAEA,OAAO,IAAI,CAAC9B,SAAS,CAAC4B,IAAI,CAAC,UAAUpB,QAAQ,EAAE;MAC7C,OAAOhB,aAAa,CAACqC,OAAO,EAAErB,QAAQ,CAAC;IACzC,CAAC,CAAC;EACJ,CAAC;EAEDN,MAAM,CAAC6B,OAAO,GAAG,SAASA,OAAOA,CAACF,OAAO,EAAE;IACzC,OAAO,IAAI,CAAC7B,SAAS,CAACoB,MAAM,CAAC,UAAUZ,QAAQ,EAAE;MAC/C,OAAOhB,aAAa,CAACqC,OAAO,EAAErB,QAAQ,CAAC;IACzC,CAAC,CAAC;EACJ,CAAC;EAEDN,MAAM,CAACgB,MAAM,GAAG,SAASA,MAAMA,CAACV,QAAQ,EAAE;IACxC,IAAIwB,MAAM,GAAG,IAAI;IAEjB1C,aAAa,CAACmC,KAAK,CAAC,YAAY;MAC9BO,MAAM,CAACC,SAAS,CAACP,OAAO,CAAC,UAAUQ,QAAQ,EAAE;QAC3CA,QAAQ,CAAC1B,QAAQ,CAAC;MACpB,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;EAEDN,MAAM,CAACiC,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAG;IAClC,IAAI,CAACC,qBAAqB,CAAC,CAAC;EAC9B,CAAC;EAEDlC,MAAM,CAACmC,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAG;IACpC,IAAI,CAACD,qBAAqB,CAAC,CAAC;EAC9B,CAAC;EAEDlC,MAAM,CAACkC,qBAAqB,GAAG,SAASA,qBAAqBA,CAAA,EAAG;IAC9D,IAAIE,eAAe,GAAG,IAAI,CAACtC,SAAS,CAACoB,MAAM,CAAC,UAAUC,CAAC,EAAE;MACvD,OAAOA,CAAC,CAACd,KAAK,CAACgC,QAAQ;IACzB,CAAC,CAAC;IACF,OAAOjD,aAAa,CAACmC,KAAK,CAAC,YAAY;MACrC,OAAOa,eAAe,CAACE,MAAM,CAAC,UAAUC,OAAO,EAAEjC,QAAQ,EAAE;QACzD,OAAOiC,OAAO,CAACC,IAAI,CAAC,YAAY;UAC9B,OAAOlC,QAAQ,CAACmC,QAAQ,CAAC,CAAC,CAACC,KAAK,CAACnD,IAAI,CAAC;QACxC,CAAC,CAAC;MACJ,CAAC,EAAEoD,OAAO,CAACC,OAAO,CAAC,CAAC,CAAC;IACvB,CAAC,CAAC;EACJ,CAAC;EAED,OAAOnD,aAAa;AACtB,CAAC,CAACD,YAAY,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}