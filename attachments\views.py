from rest_framework import generics, permissions, status
from rest_framework.response import Response
from .models import Attachment
from .serializers import AttachmentSerializer

class AttachmentUploadView(generics.CreateAPIView):
    """API endpoint for uploading files"""
    queryset = Attachment.objects.all()
    serializer_class = AttachmentSerializer
    permission_classes = [permissions.IsAuthenticated]

    def perform_create(self, serializer):
        file = self.request.data.get('file')
        serializer.save(
            uploaded_by=self.request.user,
            original_filename=file.name,
            file_size=file.size,
            content_type=file.content_type
        )

class AttachmentListView(generics.ListAPIView):
    """API endpoint to list all attachments"""
    serializer_class = AttachmentSerializer
    permission_classes = [permissions.IsAuthenticated]
    queryset = Attachment.objects.all()

    def get_queryset(self):
        return self.queryset.filter(uploaded_by=self.request.user)

class AttachmentDetailView(generics.RetrieveDestroyAPIView):
    """API endpoint to retrieve or delete a specific attachment"""
    serializer_class = AttachmentSerializer
    permission_classes = [permissions.IsAuthenticated]
    queryset = Attachment.objects.all()

    def get_queryset(self):
        return self.queryset.filter(uploaded_by=self.request.user)