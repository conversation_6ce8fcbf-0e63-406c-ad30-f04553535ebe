{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useThemeProps } from '@mui/material/styles';\nimport { GRID_DEFAULT_LOCALE_TEXT } from '../constants';\nimport { DATA_GRID_DEFAULT_SLOTS_COMPONENTS } from '../constants/defaultGridSlotsComponents';\nimport { GridEditModes } from '../models';\nimport { computeSlots, useProps, uncapitalizeObjectKeys } from '../internals/utils';\nconst DATA_GRID_FORCED_PROPS = {\n  disableMultipleColumnsFiltering: true,\n  disableMultipleColumnsSorting: true,\n  disableMultipleRowSelection: true,\n  throttleRowsMs: undefined,\n  hideFooterRowCount: false,\n  pagination: true,\n  checkboxSelectionVisibleOnly: false,\n  disableColumnReorder: true,\n  disableColumnResize: true,\n  keepColumnPositionIfDraggedOutside: false,\n  signature: 'DataGrid'\n};\n\n/**\n * The default values of `DataGridPropsWithDefaultValues` to inject in the props of DataGrid.\n */\nexport const DATA_GRID_PROPS_DEFAULT_VALUES = {\n  autoHeight: false,\n  autoPageSize: false,\n  checkboxSelection: false,\n  checkboxSelectionVisibleOnly: false,\n  columnBuffer: 3,\n  rowBuffer: 3,\n  columnThreshold: 3,\n  rowThreshold: 3,\n  rowSelection: true,\n  density: 'standard',\n  disableColumnFilter: false,\n  disableColumnMenu: false,\n  disableColumnSelector: false,\n  disableDensitySelector: false,\n  disableEval: false,\n  disableMultipleColumnsFiltering: false,\n  disableMultipleRowSelection: false,\n  disableMultipleColumnsSorting: false,\n  disableRowSelectionOnClick: false,\n  disableVirtualization: false,\n  editMode: GridEditModes.Cell,\n  filterMode: 'client',\n  filterDebounceMs: 150,\n  columnHeaderHeight: 56,\n  hideFooter: false,\n  hideFooterPagination: false,\n  hideFooterRowCount: false,\n  hideFooterSelectedRowCount: false,\n  ignoreDiacritics: false,\n  logger: console,\n  logLevel: process.env.NODE_ENV === 'production' ? 'error' : 'warn',\n  pagination: false,\n  paginationMode: 'client',\n  rowHeight: 52,\n  pageSizeOptions: [25, 50, 100],\n  rowSpacingType: 'margin',\n  showCellVerticalBorder: false,\n  showColumnVerticalBorder: false,\n  sortingOrder: ['asc', 'desc', null],\n  sortingMode: 'client',\n  throttleRowsMs: 0,\n  disableColumnReorder: false,\n  disableColumnResize: false,\n  keepNonExistentRowsSelected: false,\n  keepColumnPositionIfDraggedOutside: false,\n  unstable_ignoreValueFormatterDuringExport: false,\n  clipboardCopyCellDelimiter: '\\t',\n  rowPositionsDebounceMs: 166\n};\nconst defaultSlots = uncapitalizeObjectKeys(DATA_GRID_DEFAULT_SLOTS_COMPONENTS);\nexport const useDataGridProps = inProps => {\n  const [components, componentsProps, themedProps] = useProps(useThemeProps({\n    props: inProps,\n    name: 'MuiDataGrid'\n  }));\n  const localeText = React.useMemo(() => _extends({}, GRID_DEFAULT_LOCALE_TEXT, themedProps.localeText), [themedProps.localeText]);\n  const slots = React.useMemo(() => computeSlots({\n    defaultSlots,\n    slots: themedProps.slots,\n    components\n  }), [components, themedProps.slots]);\n  return React.useMemo(() => {\n    var _themedProps$slotProp;\n    return _extends({}, DATA_GRID_PROPS_DEFAULT_VALUES, themedProps, {\n      localeText,\n      slots,\n      slotProps: (_themedProps$slotProp = themedProps.slotProps) != null ? _themedProps$slotProp : componentsProps\n    }, DATA_GRID_FORCED_PROPS);\n  }, [themedProps, localeText, slots, componentsProps]);\n};", "map": {"version": 3, "names": ["_extends", "React", "useThemeProps", "GRID_DEFAULT_LOCALE_TEXT", "DATA_GRID_DEFAULT_SLOTS_COMPONENTS", "GridEditModes", "computeSlots", "useProps", "uncapitalizeObjectKeys", "DATA_GRID_FORCED_PROPS", "disableMultipleColumnsFiltering", "disableMultipleColumnsSorting", "disableMultipleRowSelection", "throttleRowsMs", "undefined", "hideFooterRowCount", "pagination", "checkboxSelectionVisibleOnly", "disableColumnReorder", "disableColumnResize", "keepColumnPositionIfDraggedOutside", "signature", "DATA_GRID_PROPS_DEFAULT_VALUES", "autoHeight", "autoPageSize", "checkboxSelection", "columnBuffer", "<PERSON><PERSON><PERSON><PERSON>", "columnThreshold", "rowThreshold", "rowSelection", "density", "disable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "disableColumnMenu", "disableColumnSelector", "disableDensitySelector", "disableEval", "disableRowSelectionOnClick", "disableVirtualization", "editMode", "Cell", "filterMode", "filterDebounceMs", "columnHeaderHeight", "hideFooter", "hideFooterPagination", "hideFooterSelectedRowCount", "ignoreDiacritics", "logger", "console", "logLevel", "process", "env", "NODE_ENV", "paginationMode", "rowHeight", "pageSizeOptions", "rowSpacingType", "showCellVerticalBorder", "showColumnVerticalBorder", "sortingOrder", "sortingMode", "keepNonExistentRowsSelected", "unstable_ignoreValueFormatterDuringExport", "clipboardCopyCellDelimiter", "rowPositionsDebounceMs", "defaultSlots", "useDataGridProps", "inProps", "components", "componentsProps", "themedProps", "props", "name", "localeText", "useMemo", "slots", "_themedProps$slotProp", "slotProps"], "sources": ["C:/Users/<USER>/Desktop/Test_HTML/Material Managment1/frontend/node_modules/@mui/x-data-grid/DataGrid/useDataGridProps.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useThemeProps } from '@mui/material/styles';\nimport { GRID_DEFAULT_LOCALE_TEXT } from '../constants';\nimport { DATA_GRID_DEFAULT_SLOTS_COMPONENTS } from '../constants/defaultGridSlotsComponents';\nimport { GridEditModes } from '../models';\nimport { computeSlots, useProps, uncapitalizeObjectKeys } from '../internals/utils';\nconst DATA_GRID_FORCED_PROPS = {\n  disableMultipleColumnsFiltering: true,\n  disableMultipleColumnsSorting: true,\n  disableMultipleRowSelection: true,\n  throttleRowsMs: undefined,\n  hideFooterRowCount: false,\n  pagination: true,\n  checkboxSelectionVisibleOnly: false,\n  disableColumnReorder: true,\n  disableColumnResize: true,\n  keepColumnPositionIfDraggedOutside: false,\n  signature: 'DataGrid'\n};\n\n/**\n * The default values of `DataGridPropsWithDefaultValues` to inject in the props of DataGrid.\n */\nexport const DATA_GRID_PROPS_DEFAULT_VALUES = {\n  autoHeight: false,\n  autoPageSize: false,\n  checkboxSelection: false,\n  checkboxSelectionVisibleOnly: false,\n  columnBuffer: 3,\n  rowBuffer: 3,\n  columnThreshold: 3,\n  rowThreshold: 3,\n  rowSelection: true,\n  density: 'standard',\n  disableColumnFilter: false,\n  disableColumnMenu: false,\n  disableColumnSelector: false,\n  disableDensitySelector: false,\n  disableEval: false,\n  disableMultipleColumnsFiltering: false,\n  disableMultipleRowSelection: false,\n  disableMultipleColumnsSorting: false,\n  disableRowSelectionOnClick: false,\n  disableVirtualization: false,\n  editMode: GridEditModes.Cell,\n  filterMode: 'client',\n  filterDebounceMs: 150,\n  columnHeaderHeight: 56,\n  hideFooter: false,\n  hideFooterPagination: false,\n  hideFooterRowCount: false,\n  hideFooterSelectedRowCount: false,\n  ignoreDiacritics: false,\n  logger: console,\n  logLevel: process.env.NODE_ENV === 'production' ? 'error' : 'warn',\n  pagination: false,\n  paginationMode: 'client',\n  rowHeight: 52,\n  pageSizeOptions: [25, 50, 100],\n  rowSpacingType: 'margin',\n  showCellVerticalBorder: false,\n  showColumnVerticalBorder: false,\n  sortingOrder: ['asc', 'desc', null],\n  sortingMode: 'client',\n  throttleRowsMs: 0,\n  disableColumnReorder: false,\n  disableColumnResize: false,\n  keepNonExistentRowsSelected: false,\n  keepColumnPositionIfDraggedOutside: false,\n  unstable_ignoreValueFormatterDuringExport: false,\n  clipboardCopyCellDelimiter: '\\t',\n  rowPositionsDebounceMs: 166\n};\nconst defaultSlots = uncapitalizeObjectKeys(DATA_GRID_DEFAULT_SLOTS_COMPONENTS);\nexport const useDataGridProps = inProps => {\n  const [components, componentsProps, themedProps] = useProps(useThemeProps({\n    props: inProps,\n    name: 'MuiDataGrid'\n  }));\n  const localeText = React.useMemo(() => _extends({}, GRID_DEFAULT_LOCALE_TEXT, themedProps.localeText), [themedProps.localeText]);\n  const slots = React.useMemo(() => computeSlots({\n    defaultSlots,\n    slots: themedProps.slots,\n    components\n  }), [components, themedProps.slots]);\n  return React.useMemo(() => {\n    var _themedProps$slotProp;\n    return _extends({}, DATA_GRID_PROPS_DEFAULT_VALUES, themedProps, {\n      localeText,\n      slots,\n      slotProps: (_themedProps$slotProp = themedProps.slotProps) != null ? _themedProps$slotProp : componentsProps\n    }, DATA_GRID_FORCED_PROPS);\n  }, [themedProps, localeText, slots, componentsProps]);\n};"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,QAAQ,sBAAsB;AACpD,SAASC,wBAAwB,QAAQ,cAAc;AACvD,SAASC,kCAAkC,QAAQ,yCAAyC;AAC5F,SAASC,aAAa,QAAQ,WAAW;AACzC,SAASC,YAAY,EAAEC,QAAQ,EAAEC,sBAAsB,QAAQ,oBAAoB;AACnF,MAAMC,sBAAsB,GAAG;EAC7BC,+BAA+B,EAAE,IAAI;EACrCC,6BAA6B,EAAE,IAAI;EACnCC,2BAA2B,EAAE,IAAI;EACjCC,cAAc,EAAEC,SAAS;EACzBC,kBAAkB,EAAE,KAAK;EACzBC,UAAU,EAAE,IAAI;EAChBC,4BAA4B,EAAE,KAAK;EACnCC,oBAAoB,EAAE,IAAI;EAC1BC,mBAAmB,EAAE,IAAI;EACzBC,kCAAkC,EAAE,KAAK;EACzCC,SAAS,EAAE;AACb,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMC,8BAA8B,GAAG;EAC5CC,UAAU,EAAE,KAAK;EACjBC,YAAY,EAAE,KAAK;EACnBC,iBAAiB,EAAE,KAAK;EACxBR,4BAA4B,EAAE,KAAK;EACnCS,YAAY,EAAE,CAAC;EACfC,SAAS,EAAE,CAAC;EACZC,eAAe,EAAE,CAAC;EAClBC,YAAY,EAAE,CAAC;EACfC,YAAY,EAAE,IAAI;EAClBC,OAAO,EAAE,UAAU;EACnBC,mBAAmB,EAAE,KAAK;EAC1BC,iBAAiB,EAAE,KAAK;EACxBC,qBAAqB,EAAE,KAAK;EAC5BC,sBAAsB,EAAE,KAAK;EAC7BC,WAAW,EAAE,KAAK;EAClB1B,+BAA+B,EAAE,KAAK;EACtCE,2BAA2B,EAAE,KAAK;EAClCD,6BAA6B,EAAE,KAAK;EACpC0B,0BAA0B,EAAE,KAAK;EACjCC,qBAAqB,EAAE,KAAK;EAC5BC,QAAQ,EAAElC,aAAa,CAACmC,IAAI;EAC5BC,UAAU,EAAE,QAAQ;EACpBC,gBAAgB,EAAE,GAAG;EACrBC,kBAAkB,EAAE,EAAE;EACtBC,UAAU,EAAE,KAAK;EACjBC,oBAAoB,EAAE,KAAK;EAC3B9B,kBAAkB,EAAE,KAAK;EACzB+B,0BAA0B,EAAE,KAAK;EACjCC,gBAAgB,EAAE,KAAK;EACvBC,MAAM,EAAEC,OAAO;EACfC,QAAQ,EAAEC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG,OAAO,GAAG,MAAM;EAClErC,UAAU,EAAE,KAAK;EACjBsC,cAAc,EAAE,QAAQ;EACxBC,SAAS,EAAE,EAAE;EACbC,eAAe,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC;EAC9BC,cAAc,EAAE,QAAQ;EACxBC,sBAAsB,EAAE,KAAK;EAC7BC,wBAAwB,EAAE,KAAK;EAC/BC,YAAY,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,IAAI,CAAC;EACnCC,WAAW,EAAE,QAAQ;EACrBhD,cAAc,EAAE,CAAC;EACjBK,oBAAoB,EAAE,KAAK;EAC3BC,mBAAmB,EAAE,KAAK;EAC1B2C,2BAA2B,EAAE,KAAK;EAClC1C,kCAAkC,EAAE,KAAK;EACzC2C,yCAAyC,EAAE,KAAK;EAChDC,0BAA0B,EAAE,IAAI;EAChCC,sBAAsB,EAAE;AAC1B,CAAC;AACD,MAAMC,YAAY,GAAG1D,sBAAsB,CAACJ,kCAAkC,CAAC;AAC/E,OAAO,MAAM+D,gBAAgB,GAAGC,OAAO,IAAI;EACzC,MAAM,CAACC,UAAU,EAAEC,eAAe,EAAEC,WAAW,CAAC,GAAGhE,QAAQ,CAACL,aAAa,CAAC;IACxEsE,KAAK,EAAEJ,OAAO;IACdK,IAAI,EAAE;EACR,CAAC,CAAC,CAAC;EACH,MAAMC,UAAU,GAAGzE,KAAK,CAAC0E,OAAO,CAAC,MAAM3E,QAAQ,CAAC,CAAC,CAAC,EAAEG,wBAAwB,EAAEoE,WAAW,CAACG,UAAU,CAAC,EAAE,CAACH,WAAW,CAACG,UAAU,CAAC,CAAC;EAChI,MAAME,KAAK,GAAG3E,KAAK,CAAC0E,OAAO,CAAC,MAAMrE,YAAY,CAAC;IAC7C4D,YAAY;IACZU,KAAK,EAAEL,WAAW,CAACK,KAAK;IACxBP;EACF,CAAC,CAAC,EAAE,CAACA,UAAU,EAAEE,WAAW,CAACK,KAAK,CAAC,CAAC;EACpC,OAAO3E,KAAK,CAAC0E,OAAO,CAAC,MAAM;IACzB,IAAIE,qBAAqB;IACzB,OAAO7E,QAAQ,CAAC,CAAC,CAAC,EAAEsB,8BAA8B,EAAEiD,WAAW,EAAE;MAC/DG,UAAU;MACVE,KAAK;MACLE,SAAS,EAAE,CAACD,qBAAqB,GAAGN,WAAW,CAACO,SAAS,KAAK,IAAI,GAAGD,qBAAqB,GAAGP;IAC/F,CAAC,EAAE7D,sBAAsB,CAAC;EAC5B,CAAC,EAAE,CAAC8D,WAAW,EAAEG,UAAU,EAAEE,KAAK,EAAEN,eAAe,CAAC,CAAC;AACvD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}