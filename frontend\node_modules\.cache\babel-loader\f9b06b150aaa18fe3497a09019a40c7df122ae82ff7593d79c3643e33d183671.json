{"ast": null, "code": "export { PickersArrowSwitcher } from './PickersArrowSwitcher';", "map": {"version": 3, "names": ["PickersArrowSwitcher"], "sources": ["C:/Users/<USER>/Desktop/Test_HTML/Material Managment1/frontend/node_modules/@mui/x-date-pickers/internals/components/PickersArrowSwitcher/index.js"], "sourcesContent": ["export { PickersArrowSwitcher } from './PickersArrowSwitcher';"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}