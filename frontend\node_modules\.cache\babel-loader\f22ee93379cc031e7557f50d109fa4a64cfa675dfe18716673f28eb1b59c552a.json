{"ast": null, "code": "export { default } from './getValidReactChildren';", "map": {"version": 3, "names": ["default"], "sources": ["C:/Users/<USER>/Desktop/Test_HTML/Material Managment1/frontend/node_modules/@mui/utils/esm/getValidReactChildren/index.js"], "sourcesContent": ["export { default } from './getValidReactChildren';"], "mappings": "AAAA,SAASA,OAAO,QAAQ,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}