from django.db import models
from django.conf import settings
import os

def attachment_file_path(instance, filename):
    """Generate file path for new attachment"""
    return os.path.join('attachments', str(instance.id), filename)

class Attachment(models.Model):
    """Model to store file attachments"""
    file = models.FileField(upload_to=attachment_file_path)
    original_filename = models.CharField(max_length=255)
    file_size = models.PositiveIntegerField()
    content_type = models.CharField(max_length=100)
    uploaded_at = models.DateTimeField(auto_now_add=True)
    uploaded_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)

    def __str__(self):
        return f"{self.original_filename} ({self.file_size} bytes)"