{"ast": null, "code": "import { unstable_generateUtilityClass as generateUtilityClass, unstable_generateUtilityClasses as generateUtilityClasses } from '@mui/utils';\nexport function getPickersArrowSwitcherUtilityClass(slot) {\n  return generateUtilityClass('MuiPickersArrowSwitcher', slot);\n}\nexport const pickersArrowSwitcherClasses = generateUtilityClasses('MuiPickersArrowSwitcher', ['root', 'spacer', 'button']);", "map": {"version": 3, "names": ["unstable_generateUtilityClass", "generateUtilityClass", "unstable_generateUtilityClasses", "generateUtilityClasses", "getPickersArrowSwitcherUtilityClass", "slot", "pickersArrowSwitcherClasses"], "sources": ["C:/Users/<USER>/Desktop/Test_HTML/Material Managment1/frontend/node_modules/@mui/x-date-pickers/internals/components/PickersArrowSwitcher/pickersArrowSwitcherClasses.js"], "sourcesContent": ["import { unstable_generateUtilityClass as generateUtilityClass, unstable_generateUtilityClasses as generateUtilityClasses } from '@mui/utils';\nexport function getPickersArrowSwitcherUtilityClass(slot) {\n  return generateUtilityClass('MuiPickersArrowSwitcher', slot);\n}\nexport const pickersArrowSwitcherClasses = generateUtilityClasses('MuiPickersArrowSwitcher', ['root', 'spacer', 'button']);"], "mappings": "AAAA,SAASA,6BAA6B,IAAIC,oBAAoB,EAAEC,+BAA+B,IAAIC,sBAAsB,QAAQ,YAAY;AAC7I,OAAO,SAASC,mCAAmCA,CAACC,IAAI,EAAE;EACxD,OAAOJ,oBAAoB,CAAC,yBAAyB,EAAEI,IAAI,CAAC;AAC9D;AACA,OAAO,MAAMC,2BAA2B,GAAGH,sBAAsB,CAAC,yBAAyB,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}