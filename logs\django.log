Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET / HTTP/1.1" 200 17469
Session data corrupted
"GET / HTTP/1.1" 200 17469
Session data corrupted
"GET /create-mr/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/create-mr/ HTTP/1.1" 404 6028
Session data corrupted
"GET /create-mr/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/create-mr/ HTTP/1.1" 404 6028
Session data corrupted
"GET /create-mr/ HTTP/1.1" 302 0
Not Found: /accounts/login/
"GET /accounts/login/?next=/create-mr/ HTTP/1.1" 404 6028
C:\Users\<USER>\Desktop\Test_HTML\Material Managment1\mets_material\settings.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Session data corrupted
"GET /create-mr/ HTTP/1.1" 302 0
Session data corrupted
"GET /login/?next=/create-mr/ HTTP/1.1" 200 6800
Session data corrupted
"GET /login/ HTTP/1.1" 200 6800
Session data corrupted
"POST /login/ HTTP/1.1" 302 0
"GET /dashboard/ HTTP/1.1" 200 23434
"GET /inbox/ HTTP/1.1" 200 30779
"GET /material-requests/ HTTP/1.1" 200 30785
"GET /create-mr/ HTTP/1.1" 200 77800
"GET /create-mr/ HTTP/1.1" 200 77800
"GET /workflow/ HTTP/1.1" 200 71612
"GET /users/ HTTP/1.1" 200 35609
"GET /reports/ HTTP/1.1" 200 32481
"GET /admin/ HTTP/1.1" 200 20887
"GET /admin/materials/configurablelist/add/ HTTP/1.1" 200 22866
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
Internal Server Error: /admin/materials/mrattachment/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\sqlite3\base.py", line 360, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such table: mr_attachments

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\options.py", line 719, in wrapper
    return self.admin_site.admin_view(view)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\decorators.py", line 192, in _view_wrapper
    result = _process_exception(request, e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\decorators.py", line 190, in _view_wrapper
    response = view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\views\decorators\cache.py", line 80, in _view_wrapper
    response = view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\sites.py", line 246, in inner
    return view(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\decorators.py", line 48, in _wrapper
    return bound_method(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\decorators.py", line 192, in _view_wrapper
    result = _process_exception(request, e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\decorators.py", line 190, in _view_wrapper
    response = view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\options.py", line 2024, in changelist_view
    cl = self.get_changelist_instance(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\options.py", line 867, in get_changelist_instance
    return ChangeList(
        request,
    ...<12 lines>...
        self.search_help_text,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\views\main.py", line 146, in __init__
    self.get_results(request)
    ~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\views\main.py", line 313, in get_results
    result_count = paginator.count
                   ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\functional.py", line 47, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ~~~~~~~~~^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\paginator.py", line 110, in count
    return c()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 604, in count
    return self.query.get_count(using=self.db)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 644, in get_count
    return obj.get_aggregation(using, {"__count": Count("*")})["__count"]
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 626, in get_aggregation
    result = compiler.execute_sql(SINGLE)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\compiler.py", line 1623, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 122, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 79, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\sqlite3\base.py", line 360, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such table: mr_attachments
"GET /admin/materials/mrattachment/ HTTP/1.1" 500 202212
"GET /login/ HTTP/1.1" 200 6800
"POST /login/ HTTP/1.1" 302 0
"GET /dashboard/ HTTP/1.1" 200 23434
"GET /dashboard/ HTTP/1.1" 200 23434
"GET /inbox/ HTTP/1.1" 200 30779
"GET /material-requests/ HTTP/1.1" 200 30785
"GET /create-mr/ HTTP/1.1" 200 77800
"GET /create-mr/ HTTP/1.1" 200 77800
"GET /material-requests/ HTTP/1.1" 200 30785
"GET /create-mr/ HTTP/1.1" 200 77800
"GET /workflow/ HTTP/1.1" 200 71612
"GET /users/ HTTP/1.1" 200 35609
"GET /api/ HTTP/1.1" 200 26862
"GET /admin/ HTTP/1.1" 200 20887
"GET /admin/notifications/emailtemplate/add/ HTTP/1.1" 200 21458
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
Internal Server Error: /admin/notifications/notification/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\sqlite3\base.py", line 360, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such table: notifications

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\options.py", line 719, in wrapper
    return self.admin_site.admin_view(view)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\decorators.py", line 192, in _view_wrapper
    result = _process_exception(request, e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\decorators.py", line 190, in _view_wrapper
    response = view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\views\decorators\cache.py", line 80, in _view_wrapper
    response = view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\sites.py", line 246, in inner
    return view(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\decorators.py", line 48, in _wrapper
    return bound_method(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\decorators.py", line 192, in _view_wrapper
    result = _process_exception(request, e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\decorators.py", line 190, in _view_wrapper
    response = view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\options.py", line 2024, in changelist_view
    cl = self.get_changelist_instance(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\options.py", line 867, in get_changelist_instance
    return ChangeList(
        request,
    ...<12 lines>...
        self.search_help_text,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\views\main.py", line 146, in __init__
    self.get_results(request)
    ~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\views\main.py", line 313, in get_results
    result_count = paginator.count
                   ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\functional.py", line 47, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ~~~~~~~~~^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\paginator.py", line 110, in count
    return c()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 604, in count
    return self.query.get_count(using=self.db)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 644, in get_count
    return obj.get_aggregation(using, {"__count": Count("*")})["__count"]
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 626, in get_aggregation
    result = compiler.execute_sql(SINGLE)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\compiler.py", line 1623, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 122, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 79, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\sqlite3\base.py", line 360, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such table: notifications
"GET /admin/notifications/notification/ HTTP/1.1" 500 202066
"GET /admin/notifications/emailtemplate/add/ HTTP/1.1" 200 21458
"GET /admin/auth/group/ HTTP/1.1" 200 20444
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/auth/user/ HTTP/1.1" 200 24934
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
Internal Server Error: /admin/materials/configurablelist/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\sqlite3\base.py", line 360, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such table: configurable_lists

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\options.py", line 719, in wrapper
    return self.admin_site.admin_view(view)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\decorators.py", line 192, in _view_wrapper
    result = _process_exception(request, e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\decorators.py", line 190, in _view_wrapper
    response = view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\views\decorators\cache.py", line 80, in _view_wrapper
    response = view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\sites.py", line 246, in inner
    return view(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\decorators.py", line 48, in _wrapper
    return bound_method(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\decorators.py", line 192, in _view_wrapper
    result = _process_exception(request, e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\decorators.py", line 190, in _view_wrapper
    response = view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\options.py", line 2024, in changelist_view
    cl = self.get_changelist_instance(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\options.py", line 867, in get_changelist_instance
    return ChangeList(
        request,
    ...<12 lines>...
        self.search_help_text,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\views\main.py", line 146, in __init__
    self.get_results(request)
    ~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\views\main.py", line 313, in get_results
    result_count = paginator.count
                   ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\functional.py", line 47, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ~~~~~~~~~^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\paginator.py", line 110, in count
    return c()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 604, in count
    return self.query.get_count(using=self.db)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 644, in get_count
    return obj.get_aggregation(using, {"__count": Count("*")})["__count"]
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 626, in get_aggregation
    result = compiler.execute_sql(SINGLE)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\compiler.py", line 1623, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 122, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 79, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\sqlite3\base.py", line 360, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such table: configurable_lists
"GET /admin/materials/configurablelist/ HTTP/1.1" 500 202304
"GET /admin/materials/configurablelist/ HTTP/1.1" 200 18596
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /create-mr/ HTTP/1.1" 200 77800
"GET /admin/ HTTP/1.1" 200 20887
"GET /admin/materials/mrattachment/ HTTP/1.1" 200 18324
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/materials/mrattachment/add/ HTTP/1.1" 200 23631
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
Internal Server Error: /admin/materials/materialrequest/add/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\sqlite3\base.py", line 360, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such table: workflow_templates

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\response.py", line 92, in rendered_content
    return template.render(context, self._request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\defaulttags.py", line 243, in render
    nodelist.append(node.render_annotated(context))
                    ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader_tags.py", line 210, in render
    return template.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 173, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\defaulttags.py", line 243, in render
    nodelist.append(node.render_annotated(context))
                    ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\defaulttags.py", line 243, in render
    nodelist.append(node.render_annotated(context))
                    ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\defaulttags.py", line 327, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\defaulttags.py", line 327, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 1081, in render
    return render_value_in_context(output, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 1058, in render_value_in_context
    value = str(value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\forms\utils.py", line 79, in __str__
    return self.as_widget()
           ~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\forms\boundfield.py", line 108, in as_widget
    return widget.render(
           ~~~~~~~~~~~~~^
        name=self.html_initial_name if only_initial else self.html_name,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<2 lines>...
        renderer=self.form.renderer,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\forms\widgets.py", line 329, in render
    context = self.get_context(name, value, attrs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\widgets.py", line 329, in get_context
    "rendered_widget": self.widget.render(name, value, attrs),
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\forms\widgets.py", line 329, in render
    context = self.get_context(name, value, attrs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\forms\widgets.py", line 830, in get_context
    context = super().get_context(name, value, attrs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\forms\widgets.py", line 781, in get_context
    context["widget"]["optgroups"] = self.optgroups(
                                     ~~~~~~~~~~~~~~^
        name, context["widget"]["value"], attrs
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\forms\widgets.py", line 721, in optgroups
    for index, (option_value, option_label) in enumerate(self.choices):
                                               ~~~~~~~~~^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\forms\models.py", line 1424, in __iter__
    for obj in queryset:
               ^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 502, in _iterator
    yield from iterable
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
        chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\compiler.py", line 1623, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 122, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 79, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\sqlite3\base.py", line 360, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such table: workflow_templates
"GET /admin/materials/materialrequest/add/?_to_field=id&_popup=1 HTTP/1.1" 500 661108
"GET /admin/materials/materialrequest/ HTTP/1.1" 200 19545
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/materials/mrattachment/add/ HTTP/1.1" 200 23631
"GET / HTTP/1.1" 302 0
"GET /dashboard/ HTTP/1.1" 200 23434
"GET /create-mr/ HTTP/1.1" 200 77800
C:\Users\<USER>\Desktop\Test_HTML\Material Managment1\mets_material\urls.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET / HTTP/1.1" 200 17469
"GET /login/ HTTP/1.1" 200 6800
"GET /create-mr/ HTTP/1.1" 302 0
"GET /login/?next=/create-mr/ HTTP/1.1" 200 6800
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/login/?next=/admin/ HTTP/1.1" 200 4173
"GET / HTTP/1.1" 200 17469
"GET /login/ HTTP/1.1" 200 6800
"POST /login/ HTTP/1.1" 302 0
"GET /dashboard/ HTTP/1.1" 200 23434
"GET /login/ HTTP/1.1" 302 0
"GET /dashboard/ HTTP/1.1" 200 23434
"GET /dashboard/ HTTP/1.1" 200 23434
"GET /inbox/ HTTP/1.1" 200 30779
"GET /material-requests/ HTTP/1.1" 200 30785
"GET /create-mr/ HTTP/1.1" 200 76490
"GET /evaluations/ HTTP/1.1" 200 30911
"GET /users/ HTTP/1.1" 200 35609
"GET /reports/ HTTP/1.1" 200 32481
"GET /admin/ HTTP/1.1" 200 17192
"GET /admin/workflow/workflowinstance/ HTTP/1.1" 200 18691
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/workflow/workflowinstance/add/ HTTP/1.1" 200 29749
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /static/admin/img/icon-deletelink.svg HTTP/1.1" 200 392
"GET /admin/accounts/userpermissionassignment/ HTTP/1.1" 200 19721
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/accounts/userpermissionassignment/add/ HTTP/1.1" 200 24718
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/accounts/metspermission/add/?_to_field=id&_popup=1 HTTP/1.1" 200 11142
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/workflow/workflowhistory/ HTTP/1.1" 200 17738
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/workflow/workflowtemplate/add/ HTTP/1.1" 200 25196
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/auth/group/ HTTP/1.1" 200 16112
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/auth/group/add/ HTTP/1.1" 200 25201
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/auth/user/add/ HTTP/1.1" 200 38644
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/materials/mrattachment/add/ HTTP/1.1" 200 23631
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/materials/ HTTP/1.1" 200 7569
"GET /admin/ HTTP/1.1" 200 17192
"GET /admin/accounts/metspermission/ HTTP/1.1" 200 17810
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/accounts/metspermission/add/ HTTP/1.1" 200 23343
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/materials/ HTTP/1.1" 200 7569
"GET /admin/materials/materialrequest/ HTTP/1.1" 200 19339
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/materials/materialrequest/add/ HTTP/1.1" 200 43991
"GET /static/admin/img/icon-deletelink.svg HTTP/1.1" 304 0
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
Internal Server Error: /admin/auth/user/1/delete/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Test_HTML\Material Managment1\.venv\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Test_HTML\Material Managment1\.venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 360, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such table: notifications

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Test_HTML\Material Managment1\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Desktop\Test_HTML\Material Managment1\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Desktop\Test_HTML\Material Managment1\.venv\Lib\site-packages\django\contrib\admin\options.py", line 719, in wrapper
    return self.admin_site.admin_view(view)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Test_HTML\Material Managment1\.venv\Lib\site-packages\django\utils\decorators.py", line 192, in _view_wrapper
    result = _process_exception(request, e)
  File "C:\Users\<USER>\Desktop\Test_HTML\Material Managment1\.venv\Lib\site-packages\django\utils\decorators.py", line 190, in _view_wrapper
    response = view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Desktop\Test_HTML\Material Managment1\.venv\Lib\site-packages\django\views\decorators\cache.py", line 80, in _view_wrapper
    response = view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Desktop\Test_HTML\Material Managment1\.venv\Lib\site-packages\django\contrib\admin\sites.py", line 246, in inner
    return view(request, *args, **kwargs)
  File "C:\Users\<USER>\Desktop\Test_HTML\Material Managment1\.venv\Lib\site-packages\django\utils\decorators.py", line 48, in _wrapper
    return bound_method(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\Test_HTML\Material Managment1\.venv\Lib\site-packages\django\utils\decorators.py", line 192, in _view_wrapper
    result = _process_exception(request, e)
  File "C:\Users\<USER>\Desktop\Test_HTML\Material Managment1\.venv\Lib\site-packages\django\utils\decorators.py", line 190, in _view_wrapper
    response = view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Desktop\Test_HTML\Material Managment1\.venv\Lib\site-packages\django\contrib\admin\options.py", line 2204, in delete_view
    return self._delete_view(request, object_id, extra_context)
           ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Test_HTML\Material Managment1\.venv\Lib\site-packages\django\contrib\admin\options.py", line 2234, in _delete_view
    ) = self.get_deleted_objects([obj], request)
        ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Test_HTML\Material Managment1\.venv\Lib\site-packages\django\contrib\admin\options.py", line 2199, in get_deleted_objects
    return get_deleted_objects(objs, request, self.admin_site)
  File "C:\Users\<USER>\Desktop\Test_HTML\Material Managment1\.venv\Lib\site-packages\django\contrib\admin\utils.py", line 138, in get_deleted_objects
    collector.collect(objs)
    ~~~~~~~~~~~~~~~~~^^^^^^
  File "C:\Users\<USER>\Desktop\Test_HTML\Material Managment1\.venv\Lib\site-packages\django\contrib\admin\utils.py", line 205, in collect
    return super().collect(objs, source_attr=source_attr, **kwargs)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Test_HTML\Material Managment1\.venv\Lib\site-packages\django\db\models\deletion.py", line 343, in collect
    if getattr(on_delete, "lazy_sub_objs", False) or sub_objs:
                                                     ^^^^^^^^
  File "C:\Users\<USER>\Desktop\Test_HTML\Material Managment1\.venv\Lib\site-packages\django\db\models\query.py", line 398, in __bool__
    self._fetch_all()
    ~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\Test_HTML\Material Managment1\.venv\Lib\site-packages\django\db\models\query.py", line 1949, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Test_HTML\Material Managment1\.venv\Lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
        chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size
    )
  File "C:\Users\<USER>\Desktop\Test_HTML\Material Managment1\.venv\Lib\site-packages\django\db\models\sql\compiler.py", line 1623, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Test_HTML\Material Managment1\.venv\Lib\site-packages\django\db\backends\utils.py", line 122, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Test_HTML\Material Managment1\.venv\Lib\site-packages\django\db\backends\utils.py", line 79, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Desktop\Test_HTML\Material Managment1\.venv\Lib\site-packages\django\db\backends\utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\Desktop\Test_HTML\Material Managment1\.venv\Lib\site-packages\django\db\backends\utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Test_HTML\Material Managment1\.venv\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\Desktop\Test_HTML\Material Managment1\.venv\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Test_HTML\Material Managment1\.venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 360, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such table: notifications
"GET /admin/auth/user/1/delete/?_to_field=id&_popup=1 HTTP/1.1" 500 216034
"GET /admin/ HTTP/1.1" 200 17192
"GET /admin/materials/materialrequest/add/ HTTP/1.1" 200 43991
"GET /admin/materials/materialrequest/ HTTP/1.1" 200 19339
"GET /admin/materials/ HTTP/1.1" 200 7569
"GET /admin/accounts/metspermission/add/ HTTP/1.1" 200 23343
"GET /admin/accounts/metspermission/ HTTP/1.1" 200 17810
"GET /admin/ HTTP/1.1" 200 17192
"GET /admin/materials/ HTTP/1.1" 200 7569
"GET /admin/materials/mrattachment/add/ HTTP/1.1" 200 23631
"GET /admin/auth/user/add/ HTTP/1.1" 200 38644
"GET /admin/auth/group/add/ HTTP/1.1" 200 25201
"GET /admin/auth/group/ HTTP/1.1" 200 16112
"GET /admin/workflow/workflowtemplate/add/ HTTP/1.1" 200 25196
"GET /admin/workflow/workflowhistory/ HTTP/1.1" 200 17738
"GET /admin/accounts/userpermissionassignment/add/ HTTP/1.1" 200 24718
"GET /admin/accounts/userpermissionassignment/ HTTP/1.1" 200 19721
"GET /admin/workflow/workflowinstance/add/ HTTP/1.1" 200 29749
"GET /admin/workflow/workflowinstance/ HTTP/1.1" 200 18691
"GET /admin/ HTTP/1.1" 200 17192
"GET /inbox/ HTTP/1.1" 200 30779
"GET /material-requests/ HTTP/1.1" 200 30785
"GET /create-mr/ HTTP/1.1" 200 76490
"GET /admin/ HTTP/1.1" 200 17192
"GET /admin/accounts/metspermission/add/ HTTP/1.1" 200 23343
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/ HTTP/1.1" 200 17192
"GET /admin/accounts/metsauditlog/ HTTP/1.1" 200 17782
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/accounts/metsauditlog/?action__exact=assign HTTP/1.1" 200 18073
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/materials/mritem/add/ HTTP/1.1" 200 27811
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET /admin/materials/materialrequest/add/ HTTP/1.1" 200 43991
"GET /static/admin/img/icon-deletelink.svg HTTP/1.1" 304 0
"GET /admin/jsi18n/ HTTP/1.1" 200 3342
"GET / HTTP/1.1" 302 0
"GET /dashboard/ HTTP/1.1" 200 23434
"GET /create-mr/ HTTP/1.1" 200 76490
C:\Users\<USER>\Desktop\Test_HTML\Material Managment1\mets_material\urls.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\Test_HTML\Material Managment1\mets_material\urls.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\Test_HTML\Material Managment1\mets_material\urls.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET / HTTP/1.1" 200 17469
"GET /login/ HTTP/1.1" 200 6800
"GET /create-mr/ HTTP/1.1" 302 0
"GET /login/?next=/create-mr/ HTTP/1.1" 200 6800
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/login/?next=/admin/ HTTP/1.1" 200 4173
"GET /api/manage-lists/departments/ HTTP/1.1" 200 135
"GET /create-mr/ HTTP/1.1" 200 81648
"GET /create-mr/ HTTP/1.1" 200 81648
C:\Users\<USER>\Desktop\Test_HTML\Material Managment1\mets_material\settings.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\Test_HTML\Material Managment1\materials\models.py changed, reloading.
Watching for file changes with StatReloader
Not Found: /favicon.ico
"GET /favicon.ico HTTP/1.1" 404 4841
Not Found: /manifest.json
"GET /manifest.json HTTP/1.1" 404 4847
Not Found: /manifest.json
"GET /manifest.json HTTP/1.1" 404 4847
Not Found: /manifest.json
"GET /manifest.json HTTP/1.1" 404 4847
Not Found: /manifest.json
"GET /manifest.json HTTP/1.1" 404 4847
Watching for file changes with StatReloader
Not Found: /api/auth/login/
"POST /api/auth/login/ HTTP/1.1" 404 5855
Not Found: /api/auth/login/
"POST /api/auth/login/ HTTP/1.1" 404 5855
Watching for file changes with StatReloader
Session data corrupted
"GET / HTTP/1.1" 200 17469
Not Found: /favicon.ico
"GET /favicon.ico HTTP/1.1" 404 4841
Session data corrupted
"GET /login/ HTTP/1.1" 200 6800
Session data corrupted
"POST /login/ HTTP/1.1" 302 0
"GET /dashboard/ HTTP/1.1" 200 23434
"GET /inbox/ HTTP/1.1" 200 30779
"GET /material-requests/ HTTP/1.1" 200 30785
"GET /create-mr/ HTTP/1.1" 200 81648
"GET /workflow/ HTTP/1.1" 200 71612
"GET /users/ HTTP/1.1" 200 35609
"GET /admin/ HTTP/1.1" 200 12999
"GET /static/admin/img/icon-viewlink.svg HTTP/1.1" 200 581
"GET /admin/workflow/workflowhistory/ HTTP/1.1" 200 14083
"GET /static/admin/css/changelists.css HTTP/1.1" 200 6566
"GET /static/admin/js/core.js HTTP/1.1" 200 5682
"GET /static/admin/js/vendor/jquery/jquery.js HTTP/1.1" 200 292458
"GET /static/admin/js/jquery.init.js HTTP/1.1" 200 347
"GET /static/admin/js/admin/RelatedObjectLookups.js HTTP/1.1" 200 8943
"GET /static/admin/js/actions.js HTTP/1.1" 200 7872
"GET /static/admin/js/prepopulate.js HTTP/1.1" 200 1531
"GET /static/admin/js/urlify.js HTTP/1.1" 200 7887
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /static/admin/js/vendor/xregexp/xregexp.js HTTP/1.1" 200 232381
"GET /static/admin/img/search.svg HTTP/1.1" 200 458
"GET /static/admin/js/filters.js HTTP/1.1" 200 978
"GET /admin/workflow/ HTTP/1.1" 200 6483
"GET /admin/workflow/workflowtemplate/add/ HTTP/1.1" 200 21062
"GET /static/admin/css/forms.css HTTP/1.1" 200 9047
"GET /static/admin/js/prepopulate_init.js HTTP/1.1" 200 586
"GET /static/admin/css/widgets.css HTTP/1.1" 200 11900
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /static/admin/js/change_form.js HTTP/1.1" 200 606
"GET /admin/auth/group/ HTTP/1.1" 200 12718
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /static/admin/img/tooltag-add.svg HTTP/1.1" 200 331
"GET /admin/auth/group/add/ HTTP/1.1" 200 20901
"GET /static/admin/js/SelectBox.js HTTP/1.1" 200 4530
"GET /static/admin/js/SelectFilter2.js HTTP/1.1" 200 15292
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /static/admin/img/icon-unknown.svg HTTP/1.1" 200 655
"GET /static/admin/img/selector-icons.svg HTTP/1.1" 200 3291
"GET /static/admin/img/icon-unknown-alt.svg HTTP/1.1" 200 655
"GET /admin/materials/configurablelist/add/ HTTP/1.1" 200 18904
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /admin/materials/mrattachment/add/ HTTP/1.1" 200 20116
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /admin/materials/mritem/ HTTP/1.1" 200 14852
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /admin/materials/mritem/add/ HTTP/1.1" 200 23621
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /admin/materials/materialrequest/ HTTP/1.1" 200 17040
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /admin/materials/materialrequest/add/ HTTP/1.1" 200 40482
"GET /static/admin/js/calendar.js HTTP/1.1" 200 8466
"GET /static/admin/js/admin/DateTimeShortcuts.js HTTP/1.1" 200 19319
"GET /static/admin/js/inlines.js HTTP/1.1" 200 15526
"GET /static/admin/img/icon-deletelink.svg HTTP/1.1" 200 392
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /static/admin/img/icon-calendar.svg HTTP/1.1" 200 1086
"GET /admin/workflow/workflowinstance/add/ HTTP/1.1" 200 25475
"GET /static/admin/img/icon-no.svg HTTP/1.1" 200 560
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /static/admin/img/icon-clock.svg HTTP/1.1" 200 677
"GET /admin/workflow/workflowtemplate/add/?_to_field=id&_popup=1 HTTP/1.1" 200 12255
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"POST /admin/workflow/workflowtemplate/add/?_to_field=id&_popup=1 HTTP/1.1" 200 12608
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"POST /admin/workflow/workflowtemplate/add/?_to_field=id&_popup=1 HTTP/1.1" 200 12629
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /admin/notifications/emailtemplate/ HTTP/1.1" 200 13994
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /admin/notifications/emailtemplate/add/ HTTP/1.1" 200 17488
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /admin/notifications/emailtemplate/ HTTP/1.1" 200 13994
"GET /admin/workflow/workflowinstance/add/ HTTP/1.1" 200 25475
"GET /admin/materials/materialrequest/add/ HTTP/1.1" 200 40482
"GET /admin/materials/materialrequest/ HTTP/1.1" 200 17040
"GET /admin/materials/mritem/add/ HTTP/1.1" 200 23621
"GET /admin/materials/mritem/ HTTP/1.1" 200 14852
"GET /admin/materials/mrattachment/add/ HTTP/1.1" 200 20116
"GET /admin/materials/configurablelist/add/ HTTP/1.1" 200 18904
"GET /admin/auth/group/add/ HTTP/1.1" 200 20901
"GET /admin/auth/group/ HTTP/1.1" 200 12718
"GET /admin/workflow/workflowtemplate/add/ HTTP/1.1" 200 21062
"GET /admin/workflow/ HTTP/1.1" 200 6483
"GET /admin/workflow/workflowhistory/ HTTP/1.1" 200 14083
"GET /admin/ HTTP/1.1" 200 12999
"GET /users/ HTTP/1.1" 200 35609
"GET /reports/ HTTP/1.1" 200 32481
"GET /dashboard/ HTTP/1.1" 200 23434
Watching for file changes with StatReloader
Watching for file changes with StatReloader
