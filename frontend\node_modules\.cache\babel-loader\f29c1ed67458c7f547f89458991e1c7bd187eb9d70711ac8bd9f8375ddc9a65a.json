{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"className\"];\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport { styled } from '@mui/system';\nimport { unstable_composeClasses as composeClasses } from '@mui/utils';\nimport { useGridRootProps } from '../../hooks/utils/useGridRootProps';\nimport { getDataGridUtilityClass } from '../../constants/gridClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['virtualScrollerRenderZone']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, classes);\n};\nconst VirtualScrollerRenderZoneRoot = styled('div', {\n  name: 'MuiDataGrid',\n  slot: 'VirtualScrollerRenderZone',\n  overridesResolver: (props, styles) => styles.virtualScrollerRenderZone\n})({\n  position: 'absolute',\n  display: 'flex',\n  // Prevents margin collapsing when using `getRowSpacing`\n  flexDirection: 'column'\n});\nconst GridVirtualScrollerRenderZone = /*#__PURE__*/React.forwardRef(function GridVirtualScrollerRenderZone(props, ref) {\n  const {\n      className\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const rootProps = useGridRootProps();\n  const classes = useUtilityClasses(rootProps);\n  return /*#__PURE__*/_jsx(VirtualScrollerRenderZoneRoot, _extends({\n    ref: ref,\n    className: clsx(classes.root, className),\n    ownerState: rootProps\n  }, other));\n});\nexport { GridVirtualScrollerRenderZone };", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "clsx", "styled", "unstable_composeClasses", "composeClasses", "useGridRootProps", "getDataGridUtilityClass", "jsx", "_jsx", "useUtilityClasses", "ownerState", "classes", "slots", "root", "VirtualScrollerRenderZoneRoot", "name", "slot", "overridesResolver", "props", "styles", "virtualScrollerRenderZone", "position", "display", "flexDirection", "GridVirtualScrollerRenderZone", "forwardRef", "ref", "className", "other", "rootProps"], "sources": ["C:/Users/<USER>/Desktop/Test_HTML/Material Managment1/frontend/node_modules/@mui/x-data-grid/components/virtualization/GridVirtualScrollerRenderZone.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"className\"];\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport { styled } from '@mui/system';\nimport { unstable_composeClasses as composeClasses } from '@mui/utils';\nimport { useGridRootProps } from '../../hooks/utils/useGridRootProps';\nimport { getDataGridUtilityClass } from '../../constants/gridClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['virtualScrollerRenderZone']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, classes);\n};\nconst VirtualScrollerRenderZoneRoot = styled('div', {\n  name: 'MuiDataGrid',\n  slot: 'VirtualScrollerRenderZone',\n  overridesResolver: (props, styles) => styles.virtualScrollerRenderZone\n})({\n  position: 'absolute',\n  display: 'flex',\n  // Prevents margin collapsing when using `getRowSpacing`\n  flexDirection: 'column'\n});\nconst GridVirtualScrollerRenderZone = /*#__PURE__*/React.forwardRef(function GridVirtualScrollerRenderZone(props, ref) {\n  const {\n      className\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const rootProps = useGridRootProps();\n  const classes = useUtilityClasses(rootProps);\n  return /*#__PURE__*/_jsx(VirtualScrollerRenderZoneRoot, _extends({\n    ref: ref,\n    className: clsx(classes.root, className),\n    ownerState: rootProps\n  }, other));\n});\nexport { GridVirtualScrollerRenderZone };"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,WAAW,CAAC;AAC/B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,IAAI,MAAM,MAAM;AACvB,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,uBAAuB,IAAIC,cAAc,QAAQ,YAAY;AACtE,SAASC,gBAAgB,QAAQ,oCAAoC;AACrE,SAASC,uBAAuB,QAAQ,6BAA6B;AACrE,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC;EACF,CAAC,GAAGD,UAAU;EACd,MAAME,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,2BAA2B;EACpC,CAAC;EACD,OAAOT,cAAc,CAACQ,KAAK,EAAEN,uBAAuB,EAAEK,OAAO,CAAC;AAChE,CAAC;AACD,MAAMG,6BAA6B,GAAGZ,MAAM,CAAC,KAAK,EAAE;EAClDa,IAAI,EAAE,aAAa;EACnBC,IAAI,EAAE,2BAA2B;EACjCC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACC;AAC/C,CAAC,CAAC,CAAC;EACDC,QAAQ,EAAE,UAAU;EACpBC,OAAO,EAAE,MAAM;EACf;EACAC,aAAa,EAAE;AACjB,CAAC,CAAC;AACF,MAAMC,6BAA6B,GAAG,aAAaxB,KAAK,CAACyB,UAAU,CAAC,SAASD,6BAA6BA,CAACN,KAAK,EAAEQ,GAAG,EAAE;EACrH,MAAM;MACFC;IACF,CAAC,GAAGT,KAAK;IACTU,KAAK,GAAG9B,6BAA6B,CAACoB,KAAK,EAAEnB,SAAS,CAAC;EACzD,MAAM8B,SAAS,GAAGxB,gBAAgB,CAAC,CAAC;EACpC,MAAMM,OAAO,GAAGF,iBAAiB,CAACoB,SAAS,CAAC;EAC5C,OAAO,aAAarB,IAAI,CAACM,6BAA6B,EAAEjB,QAAQ,CAAC;IAC/D6B,GAAG,EAAEA,GAAG;IACRC,SAAS,EAAE1B,IAAI,CAACU,OAAO,CAACE,IAAI,EAAEc,SAAS,CAAC;IACxCjB,UAAU,EAAEmB;EACd,CAAC,EAAED,KAAK,CAAC,CAAC;AACZ,CAAC,CAAC;AACF,SAASJ,6BAA6B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}